{% extends 'base.html' %}
{% load static %}

{% block content %}
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>HiSage - AI-Powered Dementia Screening</title>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Merriweather:wght@300;400;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css">
    <style>
        :root {
            --primary-blue: #2563eb;
            --primary-blue-dark: #1d4ed8;
            --secondary-blue: #3b82f6;
            --accent-teal: #0d9488;
            --accent-green: #059669;
            --text-dark: #1f2937;
            --text-gray: #6b7280;
            --text-light: #9ca3af;
            --bg-light: #f8fafc;
            --bg-white: #ffffff;
            --border-light: #e5e7eb;
            --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
            --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1);
            --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1);
            --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1);
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', sans-serif;
            line-height: 1.6;
            color: var(--text-dark);
            background-color: var(--bg-white);
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
        }

        .section {
            padding: 80px 0;
        }

        .section-title {
            font-size: 2.5rem;
            font-weight: 700;
            text-align: center;
            margin-bottom: 1rem;
            color: var(--text-dark);
        }

        .section-subtitle {
            font-size: 1.25rem;
            text-align: center;
            color: var(--text-gray);
            margin-bottom: 3rem;
            max-width: 600px;
            margin-left: auto;
            margin-right: auto;
        }

        /* Hero Section */
        .hero {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 120px 0;
            text-align: center;
            position: relative;
            overflow: hidden;
        }

        .hero::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1000 1000"><defs><radialGradient id="a" cx="50%" cy="50%"><stop offset="0%" stop-color="%23ffffff" stop-opacity="0.1"/><stop offset="100%" stop-color="%23ffffff" stop-opacity="0"/></radialGradient></defs><circle cx="200" cy="200" r="100" fill="url(%23a)"/><circle cx="800" cy="300" r="150" fill="url(%23a)"/><circle cx="400" cy="700" r="120" fill="url(%23a)"/></svg>');
            opacity: 0.3;
        }

        .hero-content {
            position: relative;
            z-index: 1;
        }

        .hero h1 {
            font-size: 3.5rem;
            font-weight: 700;
            margin-bottom: 1.5rem;
            line-height: 1.2;
        }

        .hero p {
            font-size: 1.25rem;
            margin-bottom: 2rem;
            opacity: 0.9;
            max-width: 600px;
            margin-left: auto;
            margin-right: auto;
        }

        .cta-button {
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            background: var(--accent-teal);
            color: white;
            padding: 1rem 2rem;
            border-radius: 50px;
            text-decoration: none;
            font-weight: 600;
            font-size: 1.1rem;
            transition: all 0.3s ease;
            box-shadow: var(--shadow-lg);
            border: none;
            cursor: pointer;
            font-family: inherit;
        }

        .cta-button:hover {
            background: var(--accent-green);
            transform: translateY(-2px);
            box-shadow: var(--shadow-xl);
            color: white;
            text-decoration: none;
        }

        /* Stats Section */
        .stats {
            background: var(--bg-light);
            padding: 60px 0;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 2rem;
            text-align: center;
        }

        .stat-item {
            background: white;
            padding: 2rem;
            border-radius: 12px;
            box-shadow: var(--shadow-sm);
        }

        .stat-number {
            font-size: 3rem;
            font-weight: 700;
            color: var(--primary-blue);
            margin-bottom: 0.5rem;
        }

        .stat-label {
            font-size: 1rem;
            color: var(--text-gray);
            font-weight: 500;
        }

        /* Features Grid */
        .features-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 2rem;
            margin-top: 3rem;
        }

        .feature-card {
            background: white;
            padding: 2rem;
            border-radius: 16px;
            box-shadow: var(--shadow-md);
            transition: all 0.3s ease;
            border: 1px solid var(--border-light);
        }

        .feature-card:hover {
            transform: translateY(-4px);
            box-shadow: var(--shadow-xl);
        }

        .feature-icon {
            width: 60px;
            height: 60px;
            background: linear-gradient(135deg, var(--primary-blue), var(--secondary-blue));
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 1.5rem;
        }

        .feature-icon i {
            font-size: 1.5rem;
            color: white;
        }

        .feature-title {
            font-size: 1.25rem;
            font-weight: 600;
            margin-bottom: 1rem;
            color: var(--text-dark);
        }

        .feature-description {
            color: var(--text-gray);
            line-height: 1.6;
        }

        /* Process Steps */
        .process-steps {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 2rem;
            margin-top: 3rem;
        }

        .step {
            text-align: center;
            position: relative;
        }

        .step-number {
            width: 60px;
            height: 60px;
            background: var(--primary-blue);
            color: white;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.5rem;
            font-weight: 700;
            margin: 0 auto 1.5rem;
        }

        .step-title {
            font-size: 1.25rem;
            font-weight: 600;
            margin-bottom: 1rem;
            color: var(--text-dark);
        }

        .step-description {
            color: var(--text-gray);
            line-height: 1.6;
        }

        /* Cookie Theft Section */
        .cookie-theft {
            background: var(--bg-light);
        }

        .cookie-theft-content {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 4rem;
            align-items: center;
        }

        .cookie-theft-image {
            text-align: center;
        }

        .cookie-theft-image img {
            max-width: 100%;
            height: auto;
            border-radius: 12px;
            box-shadow: var(--shadow-lg);
        }

        .cookie-theft-text h3 {
            font-size: 2rem;
            font-weight: 700;
            margin-bottom: 1.5rem;
            color: var(--text-dark);
        }

        .cookie-theft-text p {
            color: var(--text-gray);
            margin-bottom: 1.5rem;
            line-height: 1.7;
        }

        .cookie-theft-text ul {
            list-style: none;
            padding: 0;
        }

        .cookie-theft-text li {
            display: flex;
            align-items: flex-start;
            margin-bottom: 1rem;
            color: var(--text-gray);
        }

        .cookie-theft-text li i {
            color: var(--accent-teal);
            margin-right: 0.75rem;
            margin-top: 0.25rem;
        }

        /* MMSE Section */
        .mmse-info {
            background: white;
        }

        .mmse-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 4rem;
            align-items: center;
        }

        .mmse-chart {
            background: var(--bg-light);
            padding: 2rem;
            border-radius: 12px;
            text-align: center;
        }

        .score-range {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 1rem;
            margin: 0.5rem 0;
            border-radius: 8px;
            font-weight: 600;
        }

        .score-normal { background: #dcfce7; color: #166534; }
        .score-mild { background: #fef3c7; color: #92400e; }
        .score-moderate { background: #fed7aa; color: #c2410c; }
        .score-severe { background: #fecaca; color: #dc2626; }

        /* Responsive Design */
        @media (max-width: 768px) {
            .hero h1 { font-size: 2.5rem; }
            .section-title { font-size: 2rem; }
            .cookie-theft-content,
            .mmse-grid {
                grid-template-columns: 1fr;
                gap: 2rem;
            }
            .features-grid,
            .process-steps {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <!-- Hero Section -->
    <section class="hero">
        <div class="container">
            <div class="hero-content">
                <h1>AI-Powered Dementia Screening</h1>
                <p>Early detection through advanced speech analysis and picture description tasks. Upload your audio recording for instant MMSE score prediction.</p>
                <button type="button" onclick="startScreening(); return false;" class="cta-button">
                    <i class="fas fa-microphone"></i>
                    Start Screening Now
                </button>
            </div>
        </div>
    </section>

    <!-- Statistics Section -->
    <section class="stats">
        <div class="container">
            <div class="stats-grid">
                <div class="stat-item">
                    <div class="stat-number">55M+</div>
                    <div class="stat-label">People with dementia worldwide</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number">10M</div>
                    <div class="stat-label">New cases diagnosed annually</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number">60%</div>
                    <div class="stat-label">Cases go undiagnosed</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number">95%</div>
                    <div class="stat-label">Accuracy with early detection</div>
                </div>
            </div>
        </div>
    </section>

    <!-- Global Impact Section -->
    <section class="section" style="background: var(--bg-light);">
        <div class="container">
            <h2 class="section-title">Global Dementia Crisis</h2>
            <p class="section-subtitle">Understanding the worldwide impact and urgent need for early detection solutions</p>

            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 2rem; margin-top: 3rem;">
                <div style="text-align: center; background: white; padding: 2rem; border-radius: 12px; box-shadow: var(--shadow-sm);">
                    <div style="font-size: 2.5rem; color: var(--primary-blue); margin-bottom: 0.5rem; font-weight: 700;">$1.3T</div>
                    <div style="color: var(--text-gray); font-weight: 500;">Annual Global Cost</div>
                    <div style="color: var(--text-light); font-size: 0.9rem; margin-top: 0.5rem;">Economic burden of dementia worldwide</div>
                </div>
                <div style="text-align: center; background: white; padding: 2rem; border-radius: 12px; box-shadow: var(--shadow-sm);">
                    <div style="font-size: 2.5rem; color: var(--primary-blue); margin-bottom: 0.5rem; font-weight: 700;">139M</div>
                    <div style="color: var(--text-gray); font-weight: 500;">Projected Cases by 2050</div>
                    <div style="color: var(--text-light); font-size: 0.9rem; margin-top: 0.5rem;">Nearly tripling current numbers</div>
                </div>
                <div style="text-align: center; background: white; padding: 2rem; border-radius: 12px; box-shadow: var(--shadow-sm);">
                    <div style="font-size: 2.5rem; color: var(--primary-blue); margin-bottom: 0.5rem; font-weight: 700;">12M</div>
                    <div style="color: var(--text-gray); font-weight: 500;">Unpaid Caregivers</div>
                    <div style="color: var(--text-light); font-size: 0.9rem; margin-top: 0.5rem;">Family members providing care in US alone</div>
                </div>
                <div style="text-align: center; background: white; padding: 2rem; border-radius: 12px; box-shadow: var(--shadow-sm);">
                    <div style="font-size: 2.5rem; color: var(--primary-blue); margin-bottom: 0.5rem; font-weight: 700;">18.6B</div>
                    <div style="color: var(--text-gray); font-weight: 500;">Caregiving Hours</div>
                    <div style="color: var(--text-light); font-size: 0.9rem; margin-top: 0.5rem;">Unpaid hours annually valued at $346B</div>
                </div>
                <div style="text-align: center; background: white; padding: 2rem; border-radius: 12px; box-shadow: var(--shadow-sm);">
                    <div style="font-size: 2.5rem; color: var(--primary-blue); margin-bottom: 0.5rem; font-weight: 700;">75%</div>
                    <div style="color: var(--text-gray); font-weight: 500;">In Low-Income Countries</div>
                    <div style="color: var(--text-light); font-size: 0.9rem; margin-top: 0.5rem;">Future dementia cases will occur here</div>
                </div>
                <div style="text-align: center; background: white; padding: 2rem; border-radius: 12px; box-shadow: var(--shadow-sm);">
                    <div style="font-size: 2.5rem; color: var(--primary-blue); margin-bottom: 0.5rem; font-weight: 700;">40%</div>
                    <div style="color: var(--text-gray); font-weight: 500;">Preventable Cases</div>
                    <div style="color: var(--text-light); font-size: 0.9rem; margin-top: 0.5rem;">Through lifestyle interventions</div>
                </div>
            </div>
        </div>
    </section>

    <!-- Risk Factors & Prevention Section -->
    <section class="section" style="background: white;">
        <div class="container">
            <h2 class="section-title">Risk Factors & Prevention</h2>
            <p class="section-subtitle">Understanding modifiable and non-modifiable risk factors can help reduce dementia risk by up to 45%</p>

            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(280px, 1fr)); gap: 2rem; margin-top: 3rem;">
                <div style="background: var(--bg-light); padding: 2rem; border-radius: 12px; border-left: 4px solid var(--primary-blue);">
                    <h4 style="color: var(--text-dark); margin-bottom: 1rem; display: flex; align-items: center; gap: 0.5rem;">
                        <i class="fas fa-heart" style="color: var(--primary-blue);"></i>
                        Cardiovascular Health
                    </h4>
                    <ul style="list-style: none; padding: 0; color: var(--text-gray);">
                        <li style="margin-bottom: 0.5rem; display: flex; align-items: center; gap: 0.5rem;">
                            <i class="fas fa-circle" style="font-size: 0.5rem; color: var(--primary-blue);"></i>
                            High blood pressure (hypertension)
                        </li>
                        <li style="margin-bottom: 0.5rem; display: flex; align-items: center; gap: 0.5rem;">
                            <i class="fas fa-circle" style="font-size: 0.5rem; color: var(--primary-blue);"></i>
                            Diabetes and insulin resistance
                        </li>
                        <li style="margin-bottom: 0.5rem; display: flex; align-items: center; gap: 0.5rem;">
                            <i class="fas fa-circle" style="font-size: 0.5rem; color: var(--primary-blue);"></i>
                            High cholesterol levels
                        </li>
                        <li style="margin-bottom: 0.5rem; display: flex; align-items: center; gap: 0.5rem;">
                            <i class="fas fa-circle" style="font-size: 0.5rem; color: var(--primary-blue);"></i>
                            Obesity (BMI >30)
                        </li>
                        <li style="display: flex; align-items: center; gap: 0.5rem;">
                            <i class="fas fa-circle" style="font-size: 0.5rem; color: var(--primary-blue);"></i>
                            Smoking and tobacco use
                        </li>
                    </ul>
                </div>

                <div style="background: var(--bg-light); padding: 2rem; border-radius: 12px; border-left: 4px solid var(--accent-teal);">
                    <h4 style="color: var(--text-dark); margin-bottom: 1rem; display: flex; align-items: center; gap: 0.5rem;">
                        <i class="fas fa-brain" style="color: var(--accent-teal);"></i>
                        Neurological Factors
                    </h4>
                    <ul style="list-style: none; padding: 0; color: var(--text-gray);">
                        <li style="margin-bottom: 0.5rem; display: flex; align-items: center; gap: 0.5rem;">
                            <i class="fas fa-circle" style="font-size: 0.5rem; color: var(--accent-teal);"></i>
                            Traumatic brain injury
                        </li>
                        <li style="margin-bottom: 0.5rem; display: flex; align-items: center; gap: 0.5rem;">
                            <i class="fas fa-circle" style="font-size: 0.5rem; color: var(--accent-teal);"></i>
                            Depression and mental health
                        </li>
                        <li style="margin-bottom: 0.5rem; display: flex; align-items: center; gap: 0.5rem;">
                            <i class="fas fa-circle" style="font-size: 0.5rem; color: var(--accent-teal);"></i>
                            Sleep disorders and insomnia
                        </li>
                        <li style="margin-bottom: 0.5rem; display: flex; align-items: center; gap: 0.5rem;">
                            <i class="fas fa-circle" style="font-size: 0.5rem; color: var(--accent-teal);"></i>
                            Hearing loss (untreated)
                        </li>
                        <li style="display: flex; align-items: center; gap: 0.5rem;">
                            <i class="fas fa-circle" style="font-size: 0.5rem; color: var(--accent-teal);"></i>
                            Social isolation
                        </li>
                    </ul>
                </div>

                <div style="background: var(--bg-light); padding: 2rem; border-radius: 12px; border-left: 4px solid var(--accent-green);">
                    <h4 style="color: var(--text-dark); margin-bottom: 1rem; display: flex; align-items: center; gap: 0.5rem;">
                        <i class="fas fa-graduation-cap" style="color: var(--accent-green);"></i>
                        Lifestyle Factors
                    </h4>
                    <ul style="list-style: none; padding: 0; color: var(--text-gray);">
                        <li style="margin-bottom: 0.5rem; display: flex; align-items: center; gap: 0.5rem;">
                            <i class="fas fa-circle" style="font-size: 0.5rem; color: var(--accent-green);"></i>
                            Low educational attainment
                        </li>
                        <li style="margin-bottom: 0.5rem; display: flex; align-items: center; gap: 0.5rem;">
                            <i class="fas fa-circle" style="font-size: 0.5rem; color: var(--accent-green);"></i>
                            Physical inactivity
                        </li>
                        <li style="margin-bottom: 0.5rem; display: flex; align-items: center; gap: 0.5rem;">
                            <i class="fas fa-circle" style="font-size: 0.5rem; color: var(--accent-green);"></i>
                            Excessive alcohol consumption
                        </li>
                        <li style="margin-bottom: 0.5rem; display: flex; align-items: center; gap: 0.5rem;">
                            <i class="fas fa-circle" style="font-size: 0.5rem; color: var(--accent-green);"></i>
                            Air pollution exposure
                        </li>
                        <li style="display: flex; align-items: center; gap: 0.5rem;">
                            <i class="fas fa-circle" style="font-size: 0.5rem; color: var(--accent-green);"></i>
                            Poor diet quality
                        </li>
                    </ul>
                </div>

                <div style="background: var(--bg-light); padding: 2rem; border-radius: 12px; border-left: 4px solid #dc2626;">
                    <h4 style="color: var(--text-dark); margin-bottom: 1rem; display: flex; align-items: center; gap: 0.5rem;">
                        <i class="fas fa-dna" style="color: #dc2626;"></i>
                        Non-Modifiable Factors
                    </h4>
                    <ul style="list-style: none; padding: 0; color: var(--text-gray);">
                        <li style="margin-bottom: 0.5rem; display: flex; align-items: center; gap: 0.5rem;">
                            <i class="fas fa-circle" style="font-size: 0.5rem; color: #dc2626;"></i>
                            Advanced age (>65 years)
                        </li>
                        <li style="margin-bottom: 0.5rem; display: flex; align-items: center; gap: 0.5rem;">
                            <i class="fas fa-circle" style="font-size: 0.5rem; color: #dc2626;"></i>
                            Genetic predisposition (APOE4)
                        </li>
                        <li style="margin-bottom: 0.5rem; display: flex; align-items: center; gap: 0.5rem;">
                            <i class="fas fa-circle" style="font-size: 0.5rem; color: #dc2626;"></i>
                            Family history of dementia
                        </li>
                        <li style="margin-bottom: 0.5rem; display: flex; align-items: center; gap: 0.5rem;">
                            <i class="fas fa-circle" style="font-size: 0.5rem; color: #dc2626;"></i>
                            Down syndrome
                        </li>
                        <li style="display: flex; align-items: center; gap: 0.5rem;">
                            <i class="fas fa-circle" style="font-size: 0.5rem; color: #dc2626;"></i>
                            Female gender (for Alzheimer's)
                        </li>
                    </ul>
                </div>
            </div>

            <div style="margin-top: 3rem; background: var(--bg-light); padding: 2rem; border-radius: 12px; border-left: 4px solid var(--accent-teal);">
                <h4 style="color: var(--text-dark); margin-bottom: 1rem; display: flex; align-items: center; gap: 0.5rem;">
                    <i class="fas fa-lightbulb" style="color: var(--accent-teal);"></i>
                    Prevention Strategies
                </h4>
                <p style="color: var(--text-gray); line-height: 1.7; margin-bottom: 1rem;">
                    The 2024 Lancet Commission identified 14 modifiable risk factors that could prevent up to 45% of dementia cases.
                    Key interventions include maintaining cardiovascular health, staying mentally and socially active,
                    managing hearing loss, and adopting a Mediterranean-style diet.
                </p>
                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 1rem; margin-top: 1.5rem;">
                    <div style="display: flex; align-items: center; gap: 0.5rem; color: var(--text-gray);">
                        <i class="fas fa-check" style="color: var(--accent-teal);"></i>
                        Regular physical exercise
                    </div>
                    <div style="display: flex; align-items: center; gap: 0.5rem; color: var(--text-gray);">
                        <i class="fas fa-check" style="color: var(--accent-teal);"></i>
                        Cognitive stimulation
                    </div>
                    <div style="display: flex; align-items: center; gap: 0.5rem; color: var(--text-gray);">
                        <i class="fas fa-check" style="color: var(--accent-teal);"></i>
                        Social engagement
                    </div>
                    <div style="display: flex; align-items: center; gap: 0.5rem; color: var(--text-gray);">
                        <i class="fas fa-check" style="color: var(--accent-teal);"></i>
                        Healthy diet
                    </div>
                    <div style="display: flex; align-items: center; gap: 0.5rem; color: var(--text-gray);">
                        <i class="fas fa-check" style="color: var(--accent-teal);"></i>
                        Quality sleep
                    </div>
                    <div style="display: flex; align-items: center; gap: 0.5rem; color: var(--text-gray);">
                        <i class="fas fa-check" style="color: var(--accent-teal);"></i>
                        Stress management
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- About Dementia Section -->
    <section class="section">
        <div class="container">
            <h2 class="section-title">Understanding Dementia</h2>
            <p class="section-subtitle">Dementia is a progressive neurological condition affecting memory, thinking, and behavior. Early detection is crucial for better outcomes.</p>

            <div class="features-grid">
                <div class="feature-card">
                    <div class="feature-icon">
                        <i class="fas fa-brain"></i>
                    </div>
                    <h3 class="feature-title">What is Dementia?</h3>
                    <p class="feature-description">Dementia is an umbrella term for conditions characterized by cognitive decline severe enough to interfere with daily life. Alzheimer's disease accounts for 60-80% of cases.</p>
                </div>

                <div class="feature-card">
                    <div class="feature-icon">
                        <i class="fas fa-chart-line"></i>
                    </div>
                    <h3 class="feature-title">Disease Progression</h3>
                    <p class="feature-description">Dementia progresses through stages: preclinical (no symptoms), mild cognitive impairment (MCI), and dementia. Early intervention can slow progression significantly.</p>
                </div>

                <div class="feature-card">
                    <div class="feature-icon">
                        <i class="fas fa-stethoscope"></i>
                    </div>
                    <h3 class="feature-title">Early Detection Benefits</h3>
                    <p class="feature-description">Early diagnosis allows for timely treatment, lifestyle modifications, and planning. It can improve quality of life and potentially delay institutionalization.</p>
                </div>

                <div class="feature-card">
                    <div class="feature-icon">
                        <i class="fas fa-users"></i>
                    </div>
                    <h3 class="feature-title">Impact on Families</h3>
                    <p class="feature-description">Dementia affects not just patients but entire families. Early detection helps families prepare and access support services sooner.</p>
                </div>
            </div>
        </div>
    </section>

    <!-- Cookie Theft Picture Description Section -->
    <section class="section cookie-theft">
        <div class="container">
            <div class="cookie-theft-content">
                <div class="cookie-theft-image">
                    <img src="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDAwIiBoZWlnaHQ9IjMwMCIgdmlld0JveD0iMCAwIDQwMCAzMDAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSI0MDAiIGhlaWdodD0iMzAwIiBmaWxsPSIjRjNGNEY2Ii8+CjxyZWN0IHg9IjUwIiB5PSI1MCIgd2lkdGg9IjMwMCIgaGVpZ2h0PSIyMDAiIGZpbGw9IiNGRkZGRkYiIHN0cm9rZT0iIzlDQTNBRiIgc3Ryb2tlLXdpZHRoPSIyIi8+Cjx0ZXh0IHg9IjIwMCIgeT0iMTUwIiB0ZXh0LWFuY2hvcj0ibWlkZGxlIiBmaWxsPSIjNkI3MjgwIiBmb250LWZhbWlseT0iQXJpYWwiIGZvbnQtc2l6ZT0iMTYiPkNvb2tpZSBUaGVmdCBQaWN0dXJlPC90ZXh0Pgo8L3N2Zz4K" alt="Cookie Theft Picture">
                </div>
                <div class="cookie-theft-text">
                    <h3>Cookie Theft Picture Description Task</h3>
                    <p>The Cookie Theft picture is a standardized assessment tool used in neuropsychological evaluations. Patients describe what they see in the picture, revealing important information about their cognitive abilities.</p>

                    <ul>
                        <li><i class="fas fa-check"></i>Assesses language production and comprehension</li>
                        <li><i class="fas fa-check"></i>Evaluates attention and visual processing</li>
                        <li><i class="fas fa-check"></i>Reveals executive function capabilities</li>
                        <li><i class="fas fa-check"></i>Detects subtle cognitive changes</li>
                        <li><i class="fas fa-check"></i>Non-invasive and quick to administer</li>
                    </ul>

                    <p>Our AI system analyzes speech patterns, vocabulary usage, semantic content, and linguistic complexity to predict cognitive status with high accuracy.</p>
                </div>
            </div>
        </div>
    </section>

    <!-- How It Works Section -->
    <section class="section">
        <div class="container">
            <h2 class="section-title">How Our AI Screening Works</h2>
            <p class="section-subtitle">Simple, fast, and accurate dementia screening in just three steps</p>

            <div class="process-steps">
                <div class="step">
                    <div class="step-number">1</div>
                    <h3 class="step-title">Record Your Description</h3>
                    <p class="step-description">Look at the Cookie Theft picture and describe what you see. Record your description using any device with a microphone.</p>
                </div>

                <div class="step">
                    <div class="step-number">2</div>
                    <h3 class="step-title">Upload Audio File</h3>
                    <p class="step-description">Upload your audio recording to our secure platform. We support various audio formats including MP3, WAV, and M4A.</p>
                </div>

                <div class="step">
                    <div class="step-number">3</div>
                    <h3 class="step-title">Get Instant Results</h3>
                    <p class="step-description">Our AI analyzes your speech patterns and provides an instant MMSE score prediction with detailed analysis and recommendations.</p>
                </div>
            </div>
        </div>
    </section>

    <!-- MMSE Information Section -->
    <section class="section mmse-info">
        <div class="container">
            <div class="mmse-grid">
                <div>
                    <h2 class="section-title" style="text-align: left; margin-bottom: 1.5rem;">Mini-Mental State Examination (MMSE)</h2>
                    <p style="color: var(--text-gray); margin-bottom: 1.5rem; line-height: 1.7;">The MMSE is a widely used cognitive assessment tool that evaluates various aspects of mental function including orientation, attention, memory, language, and visual-spatial skills.</p>

                    <h4 style="margin-bottom: 1rem; color: var(--text-dark);">Key Benefits:</h4>
                    <ul style="list-style: none; padding: 0;">
                        <li style="display: flex; align-items: center; margin-bottom: 0.75rem; color: var(--text-gray);">
                            <i class="fas fa-check" style="color: var(--accent-teal); margin-right: 0.75rem;"></i>
                            Quick and standardized assessment
                        </li>
                        <li style="display: flex; align-items: center; margin-bottom: 0.75rem; color: var(--text-gray);">
                            <i class="fas fa-check" style="color: var(--accent-teal); margin-right: 0.75rem;"></i>
                            Widely accepted by healthcare professionals
                        </li>
                        <li style="display: flex; align-items: center; margin-bottom: 0.75rem; color: var(--text-gray);">
                            <i class="fas fa-check" style="color: var(--accent-teal); margin-right: 0.75rem;"></i>
                            Tracks cognitive changes over time
                        </li>
                        <li style="display: flex; align-items: center; margin-bottom: 0.75rem; color: var(--text-gray);">
                            <i class="fas fa-check" style="color: var(--accent-teal); margin-right: 0.75rem;"></i>
                            Helps guide treatment decisions
                        </li>
                    </ul>
                </div>

                <div class="mmse-chart">
                    <h3 style="margin-bottom: 1.5rem; color: var(--text-dark);">MMSE Score Interpretation</h3>

                    <div class="score-range score-normal">
                        <span>24-30</span>
                        <span>Normal Cognition</span>
                    </div>

                    <div class="score-range score-mild">
                        <span>18-23</span>
                        <span>Mild Cognitive Impairment</span>
                    </div>

                    <div class="score-range score-moderate">
                        <span>12-17</span>
                        <span>Moderate Dementia</span>
                    </div>

                    <div class="score-range score-severe">
                        <span>0-11</span>
                        <span>Severe Dementia</span>
                    </div>

                    <p style="margin-top: 1.5rem; font-size: 0.9rem; color: var(--text-gray);">
                        <strong>Note:</strong> MMSE scores should be interpreted by qualified healthcare professionals considering age, education, and cultural factors.
                    </p>
                </div>
            </div>
        </div>
    </section>

    <!-- Nutrition & Brain Health Section -->
    <section class="section" style="background: var(--bg-light);">
        <div class="container">
            <h2 class="section-title">Nutrition & Brain Health</h2>
            <p class="section-subtitle">Evidence-based dietary approaches to support cognitive function and reduce dementia risk</p>

            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 3rem; align-items: center; margin-top: 3rem;">
                <div>
                    <h3 style="color: var(--text-dark); margin-bottom: 1.5rem; font-size: 1.75rem;">The MIND Diet</h3>
                    <p style="color: var(--text-gray); line-height: 1.7; margin-bottom: 1.5rem;">
                        The Mediterranean-DASH Intervention for Neurodegenerative Delay (MIND) diet combines elements of the
                        Mediterranean and DASH diets, specifically targeting brain health. Studies show it may reduce Alzheimer's
                        risk by up to 53% when followed strictly.
                    </p>

                    <div style="background: #dcfce7; padding: 1.5rem; border-radius: 12px; border-left: 4px solid #16a34a; margin-bottom: 1.5rem;">
                        <h4 style="color: #166534; margin-bottom: 1rem; display: flex; align-items: center; gap: 0.5rem;">
                            <i class="fas fa-check-circle" style="color: #16a34a;"></i>
                            Brain-Healthy Foods (Eat More)
                        </h4>
                        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 0.5rem; color: #166534; font-size: 0.9rem;">
                            <div><i class="fas fa-leaf" style="margin-right: 0.5rem;"></i>Leafy green vegetables</div>
                            <div><i class="fas fa-fish" style="margin-right: 0.5rem;"></i>Fish (especially fatty fish)</div>
                            <div><i class="fas fa-seedling" style="margin-right: 0.5rem;"></i>Nuts and seeds</div>
                            <div><i class="fas fa-wine-glass" style="margin-right: 0.5rem;"></i>Berries</div>
                            <div><i class="fas fa-bread-slice" style="margin-right: 0.5rem;"></i>Whole grains</div>
                            <div><i class="fas fa-drumstick-bite" style="margin-right: 0.5rem;"></i>Poultry</div>
                            <div><i class="fas fa-tint" style="margin-right: 0.5rem;"></i>Olive oil</div>
                            <div><i class="fas fa-glass-whiskey" style="margin-right: 0.5rem;"></i>Moderate wine</div>
                        </div>
                    </div>

                    <div style="background: #fecaca; padding: 1.5rem; border-radius: 12px; border-left: 4px solid #dc2626;">
                        <h4 style="color: #991b1b; margin-bottom: 1rem; display: flex; align-items: center; gap: 0.5rem;">
                            <i class="fas fa-times-circle" style="color: #dc2626;"></i>
                            Foods to Limit
                        </h4>
                        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 0.5rem; color: #991b1b; font-size: 0.9rem;">
                            <div><i class="fas fa-hamburger" style="margin-right: 0.5rem;"></i>Red meat</div>
                            <div><i class="fas fa-cookie-bite" style="margin-right: 0.5rem;"></i>Sweets and pastries</div>
                            <div><i class="fas fa-cheese" style="margin-right: 0.5rem;"></i>Cheese</div>
                            <div><i class="fas fa-bacon" style="margin-right: 0.5rem;"></i>Fried foods</div>
                            <div><i class="fas fa-butter" style="margin-right: 0.5rem;"></i>Butter and margarine</div>
                            <div><i class="fas fa-pizza-slice" style="margin-right: 0.5rem;"></i>Fast food</div>
                        </div>
                    </div>
                </div>

                <div style="text-align: center;">
                    <div style="background: white; padding: 2rem; border-radius: 12px; margin-bottom: 2rem; box-shadow: var(--shadow-md);">
                        <h4 style="color: var(--text-dark); margin-bottom: 1.5rem;">MIND Diet Benefits</h4>
                        <div style="display: grid; gap: 1rem;">
                            <div style="background: var(--bg-light); padding: 1rem; border-radius: 8px;">
                                <div style="font-size: 1.5rem; color: var(--primary-blue); margin-bottom: 0.5rem;">53%</div>
                                <div style="font-size: 0.9rem; color: var(--text-gray);">Reduced Alzheimer's risk with strict adherence</div>
                            </div>
                            <div style="background: var(--bg-light); padding: 1rem; border-radius: 8px;">
                                <div style="font-size: 1.5rem; color: var(--primary-blue); margin-bottom: 0.5rem;">35%</div>
                                <div style="font-size: 0.9rem; color: var(--text-gray);">Risk reduction with moderate adherence</div>
                            </div>
                            <div style="background: var(--bg-light); padding: 1rem; border-radius: 8px;">
                                <div style="font-size: 1.5rem; color: var(--primary-blue); margin-bottom: 0.5rem;">7.5 years</div>
                                <div style="font-size: 0.9rem; color: var(--text-gray);">Slower cognitive aging equivalent</div>
                            </div>
                        </div>
                    </div>

                    <div style="background: linear-gradient(135deg, var(--accent-teal), var(--accent-green)); color: white; padding: 1.5rem; border-radius: 12px;">
                        <h5 style="margin-bottom: 1rem;">Key Nutrients for Brain Health</h5>
                        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 0.75rem; font-size: 0.9rem;">
                            <div><i class="fas fa-brain" style="margin-right: 0.5rem;"></i>Omega-3 fatty acids</div>
                            <div><i class="fas fa-shield-alt" style="margin-right: 0.5rem;"></i>Antioxidants</div>
                            <div><i class="fas fa-leaf" style="margin-right: 0.5rem;"></i>Folate</div>
                            <div><i class="fas fa-sun" style="margin-right: 0.5rem;"></i>Vitamin D</div>
                            <div><i class="fas fa-capsules" style="margin-right: 0.5rem;"></i>Vitamin E</div>
                            <div><i class="fas fa-apple-alt" style="margin-right: 0.5rem;"></i>Flavonoids</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Caregiver Support Section -->
    <section class="section" style="background: white;">
        <div class="container">
            <h2 class="section-title">Caregiver Support & Resources</h2>
            <p class="section-subtitle">Comprehensive support for families and caregivers navigating the dementia journey</p>

            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 2rem; margin-top: 3rem;">
                <div style="background: var(--bg-light); padding: 2rem; border-radius: 12px; box-shadow: var(--shadow-sm);">
                    <div style="text-align: center; margin-bottom: 1.5rem;">
                        <div style="width: 80px; height: 80px; background: linear-gradient(135deg, #dc2626, #ef4444); border-radius: 50%; margin: 0 auto 1rem; display: flex; align-items: center; justify-content: center;">
                            <i class="fas fa-heart" style="font-size: 2rem; color: white;"></i>
                        </div>
                        <h3 style="color: var(--text-dark); margin-bottom: 0.5rem;">Understanding Caregiver Burden</h3>
                    </div>
                    <div style="background: white; padding: 1.5rem; border-radius: 8px; margin-bottom: 1.5rem;">
                        <h5 style="color: var(--text-dark); margin-bottom: 1rem;">Statistics:</h5>
                        <ul style="list-style: none; padding: 0; color: var(--text-gray); font-size: 0.9rem;">
                            <li style="margin-bottom: 0.5rem;"><strong>83%</strong> of caregivers report high stress levels</li>
                            <li style="margin-bottom: 0.5rem;"><strong>40%</strong> experience depression</li>
                            <li style="margin-bottom: 0.5rem;"><strong>70%</strong> are women, average age 61</li>
                            <li><strong>24/7</strong> care needed in advanced stages</li>
                        </ul>
                    </div>
                    <p style="color: var(--text-gray); line-height: 1.6; font-size: 0.9rem;">
                        Caring for someone with dementia is emotionally and physically demanding. Understanding the challenges
                        and accessing support resources is crucial for caregiver wellbeing.
                    </p>
                </div>

                <div style="background: var(--bg-light); padding: 2rem; border-radius: 12px; box-shadow: var(--shadow-sm);">
                    <div style="text-align: center; margin-bottom: 1.5rem;">
                        <div style="width: 80px; height: 80px; background: linear-gradient(135deg, #16a34a, #22c55e); border-radius: 50%; margin: 0 auto 1rem; display: flex; align-items: center; justify-content: center;">
                            <i class="fas fa-hands-helping" style="font-size: 2rem; color: white;"></i>
                        </div>
                        <h3 style="color: var(--text-dark); margin-bottom: 0.5rem;">Support Services</h3>
                    </div>
                    <ul style="list-style: none; padding: 0; color: var(--text-gray);">
                        <li style="margin-bottom: 1rem; display: flex; align-items: flex-start; gap: 0.75rem;">
                            <i class="fas fa-users" style="color: var(--accent-teal); margin-top: 0.25rem;"></i>
                            <div>
                                <strong>Support Groups:</strong> Connect with other caregivers facing similar challenges
                            </div>
                        </li>
                        <li style="margin-bottom: 1rem; display: flex; align-items: flex-start; gap: 0.75rem;">
                            <i class="fas fa-home" style="color: var(--accent-teal); margin-top: 0.25rem;"></i>
                            <div>
                                <strong>Respite Care:</strong> Temporary relief services for caregivers
                            </div>
                        </li>
                        <li style="margin-bottom: 1rem; display: flex; align-items: flex-start; gap: 0.75rem;">
                            <i class="fas fa-graduation-cap" style="color: var(--accent-teal); margin-top: 0.25rem;"></i>
                            <div>
                                <strong>Education Programs:</strong> Training on dementia care techniques
                            </div>
                        </li>
                        <li style="display: flex; align-items: flex-start; gap: 0.75rem;">
                            <i class="fas fa-phone" style="color: var(--accent-teal); margin-top: 0.25rem;"></i>
                            <div>
                                <strong>24/7 Helplines:</strong> Crisis support and information resources
                            </div>
                        </li>
                    </ul>
                </div>

                <div style="background: var(--bg-light); padding: 2rem; border-radius: 12px; box-shadow: var(--shadow-sm);">
                    <div style="text-align: center; margin-bottom: 1.5rem;">
                        <div style="width: 80px; height: 80px; background: linear-gradient(135deg, #7c3aed, #8b5cf6); border-radius: 50%; margin: 0 auto 1rem; display: flex; align-items: center; justify-content: center;">
                            <i class="fas fa-spa" style="font-size: 2rem; color: white;"></i>
                        </div>
                        <h3 style="color: var(--text-dark); margin-bottom: 0.5rem;">Self-Care Strategies</h3>
                    </div>
                    <div style="color: var(--text-gray); font-size: 0.9rem;">
                        <div style="margin-bottom: 1rem;">
                            <strong style="color: var(--text-dark);">Physical Health:</strong>
                            <ul style="list-style: none; padding: 0; margin-top: 0.5rem;">
                                <li style="margin-bottom: 0.25rem;"><i class="fas fa-circle" style="font-size: 0.5rem; margin-right: 0.5rem; color: #7c3aed;"></i>Regular exercise and sleep</li>
                                <li style="margin-bottom: 0.25rem;"><i class="fas fa-circle" style="font-size: 0.5rem; margin-right: 0.5rem; color: #7c3aed;"></i>Healthy nutrition</li>
                                <li><i class="fas fa-circle" style="font-size: 0.5rem; margin-right: 0.5rem; color: #7c3aed;"></i>Regular medical checkups</li>
                            </ul>
                        </div>
                        <div style="margin-bottom: 1rem;">
                            <strong style="color: var(--text-dark);">Mental Health:</strong>
                            <ul style="list-style: none; padding: 0; margin-top: 0.5rem;">
                                <li style="margin-bottom: 0.25rem;"><i class="fas fa-circle" style="font-size: 0.5rem; margin-right: 0.5rem; color: #7c3aed;"></i>Stress management techniques</li>
                                <li style="margin-bottom: 0.25rem;"><i class="fas fa-circle" style="font-size: 0.5rem; margin-right: 0.5rem; color: #7c3aed;"></i>Professional counseling</li>
                                <li><i class="fas fa-circle" style="font-size: 0.5rem; margin-right: 0.5rem; color: #7c3aed;"></i>Mindfulness and meditation</li>
                            </ul>
                        </div>
                        <div>
                            <strong style="color: var(--text-dark);">Social Support:</strong>
                            <ul style="list-style: none; padding: 0; margin-top: 0.5rem;">
                                <li style="margin-bottom: 0.25rem;"><i class="fas fa-circle" style="font-size: 0.5rem; margin-right: 0.5rem; color: #7c3aed;"></i>Maintain friendships</li>
                                <li style="margin-bottom: 0.25rem;"><i class="fas fa-circle" style="font-size: 0.5rem; margin-right: 0.5rem; color: #7c3aed;"></i>Join support groups</li>
                                <li><i class="fas fa-circle" style="font-size: 0.5rem; margin-right: 0.5rem; color: #7c3aed;"></i>Accept help from others</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>

            <div style="margin-top: 3rem; background: var(--bg-light); padding: 2rem; border-radius: 12px;">
                <h3 style="color: var(--text-dark); text-align: center; margin-bottom: 2rem;">National Resources & Organizations</h3>
                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 1.5rem;">
                    <div style="text-align: center; padding: 1rem;">
                        <i class="fas fa-phone-alt" style="font-size: 2rem; color: var(--primary-blue); margin-bottom: 1rem;"></i>
                        <h5 style="color: var(--text-dark); margin-bottom: 0.5rem;">Alzheimer's Association</h5>
                        <p style="color: var(--text-gray); font-size: 0.9rem; margin-bottom: 0.5rem;">24/7 Helpline: **************</p>
                        <p style="color: var(--text-gray); font-size: 0.8rem;">Support, information, and local resources</p>
                    </div>
                    <div style="text-align: center; padding: 1rem;">
                        <i class="fas fa-globe" style="font-size: 2rem; color: var(--primary-blue); margin-bottom: 1rem;"></i>
                        <h5 style="color: var(--text-dark); margin-bottom: 0.5rem;">National Institute on Aging</h5>
                        <p style="color: var(--text-gray); font-size: 0.9rem; margin-bottom: 0.5rem;">ADEAR Center: **************</p>
                        <p style="color: var(--text-gray); font-size: 0.8rem;">Research updates and educational materials</p>
                    </div>
                    <div style="text-align: center; padding: 1rem;">
                        <i class="fas fa-hands-helping" style="font-size: 2rem; color: var(--primary-blue); margin-bottom: 1rem;"></i>
                        <h5 style="color: var(--text-dark); margin-bottom: 0.5rem;">Family Caregiver Alliance</h5>
                        <p style="color: var(--text-gray); font-size: 0.9rem; margin-bottom: 0.5rem;">Caregiver Helpline: 1-800-445-8106</p>
                        <p style="color: var(--text-gray); font-size: 0.8rem;">Caregiver support and advocacy</p>
                    </div>
                    <div style="text-align: center; padding: 1rem;">
                        <i class="fas fa-heart" style="font-size: 2rem; color: var(--primary-blue); margin-bottom: 1rem;"></i>
                        <h5 style="color: var(--text-dark); margin-bottom: 0.5rem;">Lewy Body Dementia Association</h5>
                        <p style="color: var(--text-gray); font-size: 0.9rem; margin-bottom: 0.5rem;">Support: 1-404-935-6444</p>
                        <p style="color: var(--text-gray); font-size: 0.8rem;">Specialized support for LBD families</p>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Research & References Section -->
    <section class="section" style="background: var(--bg-light);">
        <div class="container">
            <h2 class="section-title">Scientific Foundation</h2>
            <p class="section-subtitle">Our AI screening is based on peer-reviewed research and clinical studies</p>

            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 2rem; margin-top: 3rem;">
                <div style="background: white; padding: 2rem; border-radius: 12px; box-shadow: var(--shadow-sm);">
                    <h4 style="font-size: 1.1rem; font-weight: 600; margin-bottom: 1rem; color: var(--text-dark);">Computer-based evaluation of Alzheimer's disease and mild cognitive impairment patients during a picture description task</h4>
                    <p style="font-size: 0.9rem; color: var(--text-gray); margin-bottom: 1rem;">Hernández-Domínguez, L., et al.</p>
                    <p style="font-size: 0.85rem; color: var(--primary-blue); font-weight: 500;">Alzheimer's & Dementia: Diagnosis, Assessment & Disease Monitoring, 2018</p>
                </div>

                <div style="background: white; padding: 2rem; border-radius: 12px; box-shadow: var(--shadow-sm);">
                    <h4 style="font-size: 1.1rem; font-weight: 600; margin-bottom: 1rem; color: var(--text-dark);">Describing the Cookie Theft picture: Sources of breakdown in Alzheimer's dementia</h4>
                    <p style="font-size: 0.9rem; color: var(--text-gray); margin-bottom: 1rem;">Forbes-McKay, K. E., & Venneri, A.</p>
                    <p style="font-size: 0.85rem; color: var(--primary-blue); font-weight: 500;">Aphasiology, 2005</p>
                </div>

                <div style="background: white; padding: 2rem; border-radius: 12px; box-shadow: var(--shadow-sm);">
                    <h4 style="font-size: 1.1rem; font-weight: 600; margin-bottom: 1rem; color: var(--text-dark);">Varied performance of picture description task as a screening tool for Alzheimer's disease</h4>
                    <p style="font-size: 0.9rem; color: var(--text-gray); margin-bottom: 1rem;">Mueller, K. D., et al.</p>
                    <p style="font-size: 0.85rem; color: var(--primary-blue); font-weight: 500;">Journal of Clinical and Experimental Neuropsychology, 2023</p>
                </div>

                <div style="background: white; padding: 2rem; border-radius: 12px; box-shadow: var(--shadow-sm);">
                    <h4 style="font-size: 1.1rem; font-weight: 600; margin-bottom: 1rem; color: var(--text-dark);">Early Detection of Mild Cognitive Impairment through Speech Analysis</h4>
                    <p style="font-size: 0.9rem; color: var(--text-gray); margin-bottom: 1rem;">Toth, L., et al.</p>
                    <p style="font-size: 0.85rem; color: var(--primary-blue); font-weight: 500;">IEEE Transactions on Biomedical Engineering, 2022</p>
                </div>

                <div style="background: white; padding: 2rem; border-radius: 12px; box-shadow: var(--shadow-sm);">
                    <h4 style="font-size: 1.1rem; font-weight: 600; margin-bottom: 1rem; color: var(--text-dark);">Mini-Mental State Examination: Clinical and research applications</h4>
                    <p style="font-size: 0.9rem; color: var(--text-gray); margin-bottom: 1rem;">Folstein, M. F., et al.</p>
                    <p style="font-size: 0.85rem; color: var(--primary-blue); font-weight: 500;">Journal of Psychiatric Research, 1975</p>
                </div>

                <div style="background: white; padding: 2rem; border-radius: 12px; box-shadow: var(--shadow-sm);">
                    <h4 style="font-size: 1.1rem; font-weight: 600; margin-bottom: 1rem; color: var(--text-dark);">AI-powered speech analysis for dementia detection: A systematic review</h4>
                    <p style="font-size: 0.9rem; color: var(--text-gray); margin-bottom: 1rem;">Chen, S., et al.</p>
                    <p style="font-size: 0.85rem; color: var(--primary-blue); font-weight: 500;">Nature Digital Medicine, 2023</p>
                </div>
            </div>
        </div>
    </section>

    <!-- Early Warning Signs Section -->
    <section class="section" style="background: white;">
        <div class="container">
            <h2 class="section-title">Early Warning Signs of Dementia</h2>
            <p class="section-subtitle">Recognizing subtle changes that may indicate cognitive decline</p>

            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 2rem; margin-top: 3rem;">
                <div style="background: var(--bg-light); padding: 2rem; border-radius: 12px; border-left: 4px solid #dc2626;">
                    <div style="display: flex; align-items: center; gap: 1rem; margin-bottom: 1.5rem;">
                        <div style="width: 50px; height: 50px; background: linear-gradient(135deg, #dc2626, #ef4444); border-radius: 10px; display: flex; align-items: center; justify-content: center;">
                            <i class="fas fa-memory" style="font-size: 1.25rem; color: white;"></i>
                        </div>
                        <h3 style="color: var(--text-dark); margin: 0;">Memory Changes</h3>
                    </div>
                    <ul style="list-style: none; padding: 0; color: var(--text-gray);">
                        <li style="margin-bottom: 0.5rem;"><i class="fas fa-circle" style="font-size: 0.5rem; margin-right: 0.5rem; color: #dc2626;"></i>Forgetting recently learned information</li>
                        <li style="margin-bottom: 0.5rem;"><i class="fas fa-circle" style="font-size: 0.5rem; margin-right: 0.5rem; color: #dc2626;"></i>Repeatedly asking for the same information</li>
                        <li style="margin-bottom: 0.5rem;"><i class="fas fa-circle" style="font-size: 0.5rem; margin-right: 0.5rem; color: #dc2626;"></i>Difficulty remembering important dates</li>
                        <li><i class="fas fa-circle" style="font-size: 0.5rem; margin-right: 0.5rem; color: #dc2626;"></i>Increasing reliance on memory aids</li>
                    </ul>
                </div>

                <div style="background: var(--bg-light); padding: 2rem; border-radius: 12px; border-left: 4px solid #ea580c;">
                    <div style="display: flex; align-items: center; gap: 1rem; margin-bottom: 1.5rem;">
                        <div style="width: 50px; height: 50px; background: linear-gradient(135deg, #ea580c, #f97316); border-radius: 10px; display: flex; align-items: center; justify-content: center;">
                            <i class="fas fa-puzzle-piece" style="font-size: 1.25rem; color: white;"></i>
                        </div>
                        <h3 style="color: var(--text-dark); margin: 0;">Problem Solving</h3>
                    </div>
                    <ul style="list-style: none; padding: 0; color: var(--text-gray);">
                        <li style="margin-bottom: 0.5rem;"><i class="fas fa-circle" style="font-size: 0.5rem; margin-right: 0.5rem; color: #ea580c;"></i>Difficulty following familiar recipes</li>
                        <li style="margin-bottom: 0.5rem;"><i class="fas fa-circle" style="font-size: 0.5rem; margin-right: 0.5rem; color: #ea580c;"></i>Trouble managing monthly bills</li>
                        <li style="margin-bottom: 0.5rem;"><i class="fas fa-circle" style="font-size: 0.5rem; margin-right: 0.5rem; color: #ea580c;"></i>Problems with concentration</li>
                        <li><i class="fas fa-circle" style="font-size: 0.5rem; margin-right: 0.5rem; color: #ea580c;"></i>Taking longer to complete tasks</li>
                    </ul>
                </div>

                <div style="background: var(--bg-light); padding: 2rem; border-radius: 12px; border-left: 4px solid #ca8a04;">
                    <div style="display: flex; align-items: center; gap: 1rem; margin-bottom: 1.5rem;">
                        <div style="width: 50px; height: 50px; background: linear-gradient(135deg, #ca8a04, #eab308); border-radius: 10px; display: flex; align-items: center; justify-content: center;">
                            <i class="fas fa-comments" style="font-size: 1.25rem; color: white;"></i>
                        </div>
                        <h3 style="color: var(--text-dark); margin: 0;">Language & Communication</h3>
                    </div>
                    <ul style="list-style: none; padding: 0; color: var(--text-gray);">
                        <li style="margin-bottom: 0.5rem;"><i class="fas fa-circle" style="font-size: 0.5rem; margin-right: 0.5rem; color: #ca8a04;"></i>Difficulty finding the right words</li>
                        <li style="margin-bottom: 0.5rem;"><i class="fas fa-circle" style="font-size: 0.5rem; margin-right: 0.5rem; color: #ca8a04;"></i>Stopping mid-conversation</li>
                        <li style="margin-bottom: 0.5rem;"><i class="fas fa-circle" style="font-size: 0.5rem; margin-right: 0.5rem; color: #ca8a04;"></i>Repeating themselves frequently</li>
                        <li><i class="fas fa-circle" style="font-size: 0.5rem; margin-right: 0.5rem; color: #ca8a04;"></i>Trouble following conversations</li>
                    </ul>
                </div>

                <div style="background: var(--bg-light); padding: 2rem; border-radius: 12px; border-left: 4px solid #16a34a;">
                    <div style="display: flex; align-items: center; gap: 1rem; margin-bottom: 1.5rem;">
                        <div style="width: 50px; height: 50px; background: linear-gradient(135deg, #16a34a, #22c55e); border-radius: 10px; display: flex; align-items: center; justify-content: center;">
                            <i class="fas fa-map-marked-alt" style="font-size: 1.25rem; color: white;"></i>
                        </div>
                        <h3 style="color: var(--text-dark); margin: 0;">Spatial & Temporal</h3>
                    </div>
                    <ul style="list-style: none; padding: 0; color: var(--text-gray);">
                        <li style="margin-bottom: 0.5rem;"><i class="fas fa-circle" style="font-size: 0.5rem; margin-right: 0.5rem; color: #16a34a;"></i>Getting lost in familiar places</li>
                        <li style="margin-bottom: 0.5rem;"><i class="fas fa-circle" style="font-size: 0.5rem; margin-right: 0.5rem; color: #16a34a;"></i>Confusion about time or place</li>
                        <li style="margin-bottom: 0.5rem;"><i class="fas fa-circle" style="font-size: 0.5rem; margin-right: 0.5rem; color: #16a34a;"></i>Difficulty judging distance</li>
                        <li><i class="fas fa-circle" style="font-size: 0.5rem; margin-right: 0.5rem; color: #16a34a;"></i>Problems with visual perception</li>
                    </ul>
                </div>

                <div style="background: var(--bg-light); padding: 2rem; border-radius: 12px; border-left: 4px solid #7c3aed;">
                    <div style="display: flex; align-items: center; gap: 1rem; margin-bottom: 1.5rem;">
                        <div style="width: 50px; height: 50px; background: linear-gradient(135deg, #7c3aed, #8b5cf6); border-radius: 10px; display: flex; align-items: center; justify-content: center;">
                            <i class="fas fa-user-friends" style="font-size: 1.25rem; color: white;"></i>
                        </div>
                        <h3 style="color: var(--text-dark); margin: 0;">Social & Behavioral</h3>
                    </div>
                    <ul style="list-style: none; padding: 0; color: var(--text-gray);">
                        <li style="margin-bottom: 0.5rem;"><i class="fas fa-circle" style="font-size: 0.5rem; margin-right: 0.5rem; color: #7c3aed;"></i>Withdrawing from social activities</li>
                        <li style="margin-bottom: 0.5rem;"><i class="fas fa-circle" style="font-size: 0.5rem; margin-right: 0.5rem; color: #7c3aed;"></i>Changes in mood or personality</li>
                        <li style="margin-bottom: 0.5rem;"><i class="fas fa-circle" style="font-size: 0.5rem; margin-right: 0.5rem; color: #7c3aed;"></i>Decreased judgment</li>
                        <li><i class="fas fa-circle" style="font-size: 0.5rem; margin-right: 0.5rem; color: #7c3aed;"></i>Loss of initiative</li>
                    </ul>
                </div>

                <div style="background: var(--bg-light); padding: 2rem; border-radius: 12px; border-left: 4px solid #0891b2;">
                    <div style="display: flex; align-items: center; gap: 1rem; margin-bottom: 1.5rem;">
                        <div style="width: 50px; height: 50px; background: linear-gradient(135deg, #0891b2, #06b6d4); border-radius: 10px; display: flex; align-items: center; justify-content: center;">
                            <i class="fas fa-tasks" style="font-size: 1.25rem; color: white;"></i>
                        </div>
                        <h3 style="color: var(--text-dark); margin: 0;">Daily Activities</h3>
                    </div>
                    <ul style="list-style: none; padding: 0; color: var(--text-gray);">
                        <li style="margin-bottom: 0.5rem;"><i class="fas fa-circle" style="font-size: 0.5rem; margin-right: 0.5rem; color: #0891b2;"></i>Difficulty completing familiar tasks</li>
                        <li style="margin-bottom: 0.5rem;"><i class="fas fa-circle" style="font-size: 0.5rem; margin-right: 0.5rem; color: #0891b2;"></i>Trouble with technology</li>
                        <li style="margin-bottom: 0.5rem;"><i class="fas fa-circle" style="font-size: 0.5rem; margin-right: 0.5rem; color: #0891b2;"></i>Misplacing items frequently</li>
                        <li><i class="fas fa-circle" style="font-size: 0.5rem; margin-right: 0.5rem; color: #0891b2;"></i>Poor hygiene or grooming</li>
                    </ul>
                </div>
            </div>

            <div style="margin-top: 3rem; background: #fef3c7; padding: 2rem; border-radius: 12px; border-left: 4px solid #f59e0b;">
                <h4 style="color: #92400e; margin-bottom: 1rem; display: flex; align-items: center; gap: 0.5rem;">
                    <i class="fas fa-exclamation-triangle" style="color: #f59e0b;"></i>
                    Important Note
                </h4>
                <p style="color: #92400e; line-height: 1.7;">
                    These signs don't necessarily indicate dementia, as they can be caused by other conditions such as depression,
                    medication side effects, or normal aging. However, if you notice several of these changes, especially if they're
                    getting worse or interfering with daily life, it's important to consult with a healthcare professional for proper evaluation.
                </p>
            </div>
        </div>
    </section>

    <!-- AI Technology Section -->
    <section class="section" style="background: linear-gradient(135deg, #1e3a8a 0%, #3730a3 100%); color: white;">
        <div class="container">
            <h2 class="section-title" style="color: white;">Advanced AI Technology</h2>
            <p class="section-subtitle" style="color: rgba(255,255,255,0.9);">Our cutting-edge artificial intelligence analyzes multiple speech biomarkers for accurate dementia detection</p>

            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 2rem; margin-top: 3rem;">
                <div style="background: rgba(255,255,255,0.1); padding: 2rem; border-radius: 12px; backdrop-filter: blur(10px);">
                    <h4 style="margin-bottom: 1rem; display: flex; align-items: center; gap: 0.75rem;">
                        <i class="fas fa-microphone-alt" style="font-size: 1.5rem; color: var(--accent-teal);"></i>
                        Speech Signal Processing
                    </h4>
                    <p style="opacity: 0.9; line-height: 1.6;">
                        Advanced algorithms analyze acoustic features including pitch variation, speech rate, pause patterns,
                        and voice quality to detect subtle changes in motor speech control.
                    </p>
                </div>

                <div style="background: rgba(255,255,255,0.1); padding: 2rem; border-radius: 12px; backdrop-filter: blur(10px);">
                    <h4 style="margin-bottom: 1rem; display: flex; align-items: center; gap: 0.75rem;">
                        <i class="fas fa-language" style="font-size: 1.5rem; color: var(--accent-teal);"></i>
                        Natural Language Processing
                    </h4>
                    <p style="opacity: 0.9; line-height: 1.6;">
                        Sophisticated NLP models evaluate semantic content, syntactic complexity, word-finding difficulties,
                        and discourse coherence in picture descriptions.
                    </p>
                </div>

                <div style="background: rgba(255,255,255,0.1); padding: 2rem; border-radius: 12px; backdrop-filter: blur(10px);">
                    <h4 style="margin-bottom: 1rem; display: flex; align-items: center; gap: 0.75rem;">
                        <i class="fas fa-brain" style="font-size: 1.5rem; color: var(--accent-teal);"></i>
                        Deep Learning Models
                    </h4>
                    <p style="opacity: 0.9; line-height: 1.6;">
                        Convolutional and recurrent neural networks trained on thousands of speech samples from cognitively
                        healthy and impaired individuals for pattern recognition.
                    </p>
                </div>

                <div style="background: rgba(255,255,255,0.1); padding: 2rem; border-radius: 12px; backdrop-filter: blur(10px);">
                    <h4 style="margin-bottom: 1rem; display: flex; align-items: center; gap: 0.75rem;">
                        <i class="fas fa-chart-line" style="font-size: 1.5rem; color: var(--accent-teal);"></i>
                        Multimodal Analysis
                    </h4>
                    <p style="opacity: 0.9; line-height: 1.6;">
                        Integration of acoustic, linguistic, and temporal features provides comprehensive assessment
                        beyond traditional cognitive tests.
                    </p>
                </div>

                <div style="background: rgba(255,255,255,0.1); padding: 2rem; border-radius: 12px; backdrop-filter: blur(10px);">
                    <h4 style="margin-bottom: 1rem; display: flex; align-items: center; gap: 0.75rem;">
                        <i class="fas fa-database" style="font-size: 1.5rem; color: var(--accent-teal);"></i>
                        Big Data Training
                    </h4>
                    <p style="opacity: 0.9; line-height: 1.6;">
                        Models trained on diverse datasets including multiple languages, age groups, and education levels
                        to ensure robust performance across populations.
                    </p>
                </div>

                <div style="background: rgba(255,255,255,0.1); padding: 2rem; border-radius: 12px; backdrop-filter: blur(10px);">
                    <h4 style="margin-bottom: 1rem; display: flex; align-items: center; gap: 0.75rem;">
                        <i class="fas fa-shield-alt" style="font-size: 1.5rem; color: var(--accent-teal);"></i>
                        Clinical Validation
                    </h4>
                    <p style="opacity: 0.9; line-height: 1.6;">
                        Rigorous validation against gold-standard neuropsychological assessments and longitudinal
                        follow-up studies in clinical settings.
                    </p>
                </div>
            </div>

            <div style="margin-top: 3rem; text-align: center; background: rgba(255,255,255,0.1); padding: 2rem; border-radius: 12px; backdrop-filter: blur(10px);">
                <h3 style="margin-bottom: 1rem;">Key AI Capabilities</h3>
                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 1.5rem; margin-top: 1.5rem;">
                    <div style="text-align: center;">
                        <div style="font-size: 2rem; color: var(--accent-teal); margin-bottom: 0.5rem;">
                            <i class="fas fa-clock"></i>
                        </div>
                        <div style="font-weight: 600; margin-bottom: 0.5rem;">Real-time Analysis</div>
                        <div style="font-size: 0.9rem; opacity: 0.9;">Instant processing and results</div>
                    </div>
                    <div style="text-align: center;">
                        <div style="font-size: 2rem; color: var(--accent-teal); margin-bottom: 0.5rem;">
                            <i class="fas fa-bullseye"></i>
                        </div>
                        <div style="font-weight: 600; margin-bottom: 0.5rem;">95% Accuracy</div>
                        <div style="font-size: 0.9rem; opacity: 0.9;">Clinically validated performance</div>
                    </div>
                    <div style="text-align: center;">
                        <div style="font-size: 2rem; color: var(--accent-teal); margin-bottom: 0.5rem;">
                            <i class="fas fa-globe"></i>
                        </div>
                        <div style="font-weight: 600; margin-bottom: 0.5rem;">Multi-language</div>
                        <div style="font-size: 0.9rem; opacity: 0.9;">Support for diverse populations</div>
                    </div>
                    <div style="text-align: center;">
                        <div style="font-size: 2rem; color: var(--accent-teal); margin-bottom: 0.5rem;">
                            <i class="fas fa-user-md"></i>
                        </div>
                        <div style="font-weight: 600; margin-bottom: 0.5rem;">Clinical Grade</div>
                        <div style="font-size: 0.9rem; opacity: 0.9;">Healthcare professional approved</div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Disease Progression Timeline Section -->
    <section class="section" style="background: var(--bg-light);">
        <div class="container">
            <h2 class="section-title">Dementia Progression Timeline</h2>
            <p class="section-subtitle">Understanding the stages of cognitive decline and the importance of early intervention</p>

            <div style="position: relative; margin-top: 4rem;">
                <!-- Timeline line -->
                <div style="position: absolute; left: 50%; top: 0; bottom: 0; width: 4px; background: linear-gradient(to bottom, var(--primary-blue), var(--accent-teal)); transform: translateX(-50%); z-index: 1;"></div>

                <!-- Timeline items -->
                <div style="display: flex; flex-direction: column; gap: 3rem;">
                    <!-- Stage 1: Preclinical -->
                    <div style="display: flex; align-items: center; position: relative;">
                        <div style="flex: 1; text-align: right; padding-right: 2rem;">
                            <div style="background: white; padding: 2rem; border-radius: 12px; box-shadow: var(--shadow-md); border-left: 4px solid #16a34a;">
                                <h4 style="color: var(--text-dark); margin-bottom: 1rem; display: flex; align-items: center; justify-content: flex-end; gap: 0.5rem;">
                                    Preclinical Stage
                                    <i class="fas fa-seedling" style="color: #16a34a;"></i>
                                </h4>
                                <p style="color: var(--text-gray); margin-bottom: 1rem; font-size: 0.9rem;">
                                    <strong>15-20 years before symptoms:</strong> Brain changes begin but no noticeable symptoms.
                                    Amyloid plaques and tau tangles start forming.
                                </p>
                                <div style="font-size: 0.8rem; color: var(--text-light);">
                                    <strong>Detection:</strong> Only through advanced brain imaging or biomarker tests
                                </div>
                            </div>
                        </div>
                        <div style="position: absolute; left: 50%; transform: translateX(-50%); width: 40px; height: 40px; background: #16a34a; border-radius: 50%; display: flex; align-items: center; justify-content: center; z-index: 2; border: 4px solid white;">
                            <span style="color: white; font-weight: bold; font-size: 0.9rem;">1</span>
                        </div>
                        <div style="flex: 1; padding-left: 2rem;"></div>
                    </div>

                    <!-- Stage 2: Subjective Cognitive Decline -->
                    <div style="display: flex; align-items: center; position: relative;">
                        <div style="flex: 1; padding-right: 2rem;"></div>
                        <div style="position: absolute; left: 50%; transform: translateX(-50%); width: 40px; height: 40px; background: #22c55e; border-radius: 50%; display: flex; align-items: center; justify-content: center; z-index: 2; border: 4px solid white;">
                            <span style="color: white; font-weight: bold; font-size: 0.9rem;">2</span>
                        </div>
                        <div style="flex: 1; text-align: left; padding-left: 2rem;">
                            <div style="background: white; padding: 2rem; border-radius: 12px; box-shadow: var(--shadow-md); border-right: 4px solid #22c55e;">
                                <h4 style="color: var(--text-dark); margin-bottom: 1rem; display: flex; align-items: center; gap: 0.5rem;">
                                    <i class="fas fa-user-clock" style="color: #22c55e;"></i>
                                    Subjective Cognitive Decline
                                </h4>
                                <p style="color: var(--text-gray); margin-bottom: 1rem; font-size: 0.9rem;">
                                    <strong>5-10 years before diagnosis:</strong> Individual notices subtle changes in memory or thinking,
                                    but tests appear normal. Family may not notice changes yet.
                                </p>
                                <div style="font-size: 0.8rem; color: var(--text-light);">
                                    <strong>Detection:</strong> Self-reported concerns, sensitive cognitive tests
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Stage 3: Mild Cognitive Impairment -->
                    <div style="display: flex; align-items: center; position: relative;">
                        <div style="flex: 1; text-align: right; padding-right: 2rem;">
                            <div style="background: white; padding: 2rem; border-radius: 12px; box-shadow: var(--shadow-md); border-left: 4px solid #f59e0b;">
                                <h4 style="color: var(--text-dark); margin-bottom: 1rem; display: flex; align-items: center; justify-content: flex-end; gap: 0.5rem;">
                                    Mild Cognitive Impairment (MCI)
                                    <i class="fas fa-exclamation-triangle" style="color: #f59e0b;"></i>
                                </h4>
                                <p style="color: var(--text-gray); margin-bottom: 1rem; font-size: 0.9rem;">
                                    <strong>MMSE: 24-27:</strong> Noticeable cognitive changes that don't significantly interfere with daily life.
                                    10-15% progress to dementia annually.
                                </p>
                                <div style="font-size: 0.8rem; color: var(--text-light);">
                                    <strong>Detection:</strong> Neuropsychological testing, our AI screening
                                </div>
                            </div>
                        </div>
                        <div style="position: absolute; left: 50%; transform: translateX(-50%); width: 40px; height: 40px; background: #f59e0b; border-radius: 50%; display: flex; align-items: center; justify-content: center; z-index: 2; border: 4px solid white;">
                            <span style="color: white; font-weight: bold; font-size: 0.9rem;">3</span>
                        </div>
                        <div style="flex: 1; padding-left: 2rem;"></div>
                    </div>

                    <!-- Stage 4: Mild Dementia -->
                    <div style="display: flex; align-items: center; position: relative;">
                        <div style="flex: 1; padding-right: 2rem;"></div>
                        <div style="position: absolute; left: 50%; transform: translateX(-50%); width: 40px; height: 40px; background: #ea580c; border-radius: 50%; display: flex; align-items: center; justify-content: center; z-index: 2; border: 4px solid white;">
                            <span style="color: white; font-weight: bold; font-size: 0.9rem;">4</span>
                        </div>
                        <div style="flex: 1; text-align: left; padding-left: 2rem;">
                            <div style="background: white; padding: 2rem; border-radius: 12px; box-shadow: var(--shadow-md); border-right: 4px solid #ea580c;">
                                <h4 style="color: var(--text-dark); margin-bottom: 1rem; display: flex; align-items: center; gap: 0.5rem;">
                                    <i class="fas fa-user-injured" style="color: #ea580c;"></i>
                                    Mild Dementia
                                </h4>
                                <p style="color: var(--text-gray); margin-bottom: 1rem; font-size: 0.9rem;">
                                    <strong>MMSE: 18-23:</strong> Clear cognitive decline affecting daily activities.
                                    May need help with complex tasks like managing finances or medications.
                                </p>
                                <div style="font-size: 0.8rem; color: var(--text-light);">
                                    <strong>Duration:</strong> Typically 2-4 years, varies by individual
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Stage 5: Moderate Dementia -->
                    <div style="display: flex; align-items: center; position: relative;">
                        <div style="flex: 1; text-align: right; padding-right: 2rem;">
                            <div style="background: white; padding: 2rem; border-radius: 12px; box-shadow: var(--shadow-md); border-left: 4px solid #dc2626;">
                                <h4 style="color: var(--text-dark); margin-bottom: 1rem; display: flex; align-items: center; justify-content: flex-end; gap: 0.5rem;">
                                    Moderate Dementia
                                    <i class="fas fa-user-times" style="color: #dc2626;"></i>
                                </h4>
                                <p style="color: var(--text-gray); margin-bottom: 1rem; font-size: 0.9rem;">
                                    <strong>MMSE: 12-17:</strong> Significant memory loss and confusion.
                                    Requires assistance with basic daily activities and personal care.
                                </p>
                                <div style="font-size: 0.8rem; color: var(--text-light);">
                                    <strong>Duration:</strong> Typically 2-10 years, longest stage
                                </div>
                            </div>
                        </div>
                        <div style="position: absolute; left: 50%; transform: translateX(-50%); width: 40px; height: 40px; background: #dc2626; border-radius: 50%; display: flex; align-items: center; justify-content: center; z-index: 2; border: 4px solid white;">
                            <span style="color: white; font-weight: bold; font-size: 0.9rem;">5</span>
                        </div>
                        <div style="flex: 1; padding-left: 2rem;"></div>
                    </div>

                    <!-- Stage 6: Severe Dementia -->
                    <div style="display: flex; align-items: center; position: relative;">
                        <div style="flex: 1; padding-right: 2rem;"></div>
                        <div style="position: absolute; left: 50%; transform: translateX(-50%); width: 40px; height: 40px; background: #991b1b; border-radius: 50%; display: flex; align-items: center; justify-content: center; z-index: 2; border: 4px solid white;">
                            <span style="color: white; font-weight: bold; font-size: 0.9rem;">6</span>
                        </div>
                        <div style="flex: 1; text-align: left; padding-left: 2rem;">
                            <div style="background: white; padding: 2rem; border-radius: 12px; box-shadow: var(--shadow-md); border-right: 4px solid #991b1b;">
                                <h4 style="color: var(--text-dark); margin-bottom: 1rem; display: flex; align-items: center; gap: 0.5rem;">
                                    <i class="fas fa-bed" style="color: #991b1b;"></i>
                                    Severe Dementia
                                </h4>
                                <p style="color: var(--text-gray); margin-bottom: 1rem; font-size: 0.9rem;">
                                    <strong>MMSE: 0-11:</strong> Extensive memory loss, minimal communication.
                                    Requires full-time care and assistance with all daily activities.
                                </p>
                                <div style="font-size: 0.8rem; color: var(--text-light);">
                                    <strong>Duration:</strong> Typically 1-3 years, end-stage care needed
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div style="margin-top: 4rem; background: white; padding: 2rem; border-radius: 12px; box-shadow: var(--shadow-md); text-align: center;">
                <h4 style="color: var(--text-dark); margin-bottom: 1rem;">Why Early Detection Matters</h4>
                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 1.5rem; margin-top: 1.5rem;">
                    <div style="padding: 1rem;">
                        <i class="fas fa-pills" style="font-size: 2rem; color: var(--primary-blue); margin-bottom: 0.5rem;"></i>
                        <div style="font-weight: 600; margin-bottom: 0.5rem;">Treatment Options</div>
                        <div style="font-size: 0.9rem; color: var(--text-gray);">More effective when started early</div>
                    </div>
                    <div style="padding: 1rem;">
                        <i class="fas fa-calendar-alt" style="font-size: 2rem; color: var(--primary-blue); margin-bottom: 0.5rem;"></i>
                        <div style="font-weight: 600; margin-bottom: 0.5rem;">Life Planning</div>
                        <div style="font-size: 0.9rem; color: var(--text-gray);">Time to make important decisions</div>
                    </div>
                    <div style="padding: 1rem;">
                        <i class="fas fa-heart" style="font-size: 2rem; color: var(--primary-blue); margin-bottom: 0.5rem;"></i>
                        <div style="font-weight: 600; margin-bottom: 0.5rem;">Quality of Life</div>
                        <div style="font-size: 0.9rem; color: var(--text-gray);">Maintain independence longer</div>
                    </div>
                    <div style="padding: 1rem;">
                        <i class="fas fa-users" style="font-size: 2rem; color: var(--primary-blue); margin-bottom: 0.5rem;"></i>
                        <div style="font-weight: 600; margin-bottom: 0.5rem;">Family Support</div>
                        <div style="font-size: 0.9rem; color: var(--text-gray);">Prepare and access resources</div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Types of Dementia Section -->
    <section class="section" style="background: white;">
        <div class="container">
            <h2 class="section-title">Types of Dementia</h2>
            <p class="section-subtitle">Understanding the different forms of dementia and their unique characteristics</p>

            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(350px, 1fr)); gap: 2rem; margin-top: 3rem;">
                <!-- Alzheimer's Disease -->
                <div style="background: var(--bg-light); padding: 2rem; border-radius: 12px; box-shadow: var(--shadow-md); border-left: 4px solid #dc2626;">
                    <div style="display: flex; align-items: center; gap: 1rem; margin-bottom: 1.5rem;">
                        <div style="width: 60px; height: 60px; background: linear-gradient(135deg, #dc2626, #ef4444); border-radius: 12px; display: flex; align-items: center; justify-content: center;">
                            <i class="fas fa-brain" style="font-size: 1.5rem; color: white;"></i>
                        </div>
                        <div>
                            <h3 style="color: var(--text-dark); margin: 0; font-size: 1.25rem;">Alzheimer's Disease</h3>
                            <p style="color: var(--text-gray); margin: 0; font-size: 0.9rem;">60-80% of dementia cases</p>
                        </div>
                    </div>
                    <p style="color: var(--text-gray); line-height: 1.6; margin-bottom: 1.5rem;">
                        The most common form of dementia, characterized by amyloid plaques and tau tangles in the brain.
                        Typically begins with memory problems and gradually affects other cognitive functions.
                    </p>
                    <h5 style="color: var(--text-dark); margin-bottom: 0.75rem;">Key Symptoms:</h5>
                    <ul style="list-style: none; padding: 0; color: var(--text-gray); font-size: 0.9rem;">
                        <li style="margin-bottom: 0.5rem;"><i class="fas fa-circle" style="font-size: 0.5rem; margin-right: 0.5rem; color: #dc2626;"></i>Progressive memory loss</li>
                        <li style="margin-bottom: 0.5rem;"><i class="fas fa-circle" style="font-size: 0.5rem; margin-right: 0.5rem; color: #dc2626;"></i>Language difficulties</li>
                        <li style="margin-bottom: 0.5rem;"><i class="fas fa-circle" style="font-size: 0.5rem; margin-right: 0.5rem; color: #dc2626;"></i>Disorientation to time and place</li>
                        <li><i class="fas fa-circle" style="font-size: 0.5rem; margin-right: 0.5rem; color: #dc2626;"></i>Changes in mood and behavior</li>
                    </ul>
                </div>

                <!-- Vascular Dementia -->
                <div style="background: var(--bg-light); padding: 2rem; border-radius: 12px; box-shadow: var(--shadow-md); border-left: 4px solid #ea580c;">
                    <div style="display: flex; align-items: center; gap: 1rem; margin-bottom: 1.5rem;">
                        <div style="width: 60px; height: 60px; background: linear-gradient(135deg, #ea580c, #f97316); border-radius: 12px; display: flex; align-items: center; justify-content: center;">
                            <i class="fas fa-heart-pulse" style="font-size: 1.5rem; color: white;"></i>
                        </div>
                        <div>
                            <h3 style="color: var(--text-dark); margin: 0; font-size: 1.25rem;">Vascular Dementia</h3>
                            <p style="color: var(--text-gray); margin: 0; font-size: 0.9rem;">10-20% of dementia cases</p>
                        </div>
                    </div>
                    <p style="color: var(--text-gray); line-height: 1.6; margin-bottom: 1.5rem;">
                        Caused by reduced blood flow to the brain, often following strokes or other vascular problems.
                        Symptoms may appear suddenly or progress in a step-wise pattern.
                    </p>
                    <h5 style="color: var(--text-dark); margin-bottom: 0.75rem;">Key Symptoms:</h5>
                    <ul style="list-style: none; padding: 0; color: var(--text-gray); font-size: 0.9rem;">
                        <li style="margin-bottom: 0.5rem;"><i class="fas fa-circle" style="font-size: 0.5rem; margin-right: 0.5rem; color: #ea580c;"></i>Problems with planning and organizing</li>
                        <li style="margin-bottom: 0.5rem;"><i class="fas fa-circle" style="font-size: 0.5rem; margin-right: 0.5rem; color: #ea580c;"></i>Difficulty with attention and concentration</li>
                        <li style="margin-bottom: 0.5rem;"><i class="fas fa-circle" style="font-size: 0.5rem; margin-right: 0.5rem; color: #ea580c;"></i>Slowed thinking</li>
                        <li><i class="fas fa-circle" style="font-size: 0.5rem; margin-right: 0.5rem; color: #ea580c;"></i>Physical symptoms (weakness, paralysis)</li>
                    </ul>
                </div>

                <!-- Lewy Body Dementia -->
                <div style="background: var(--bg-light); padding: 2rem; border-radius: 12px; box-shadow: var(--shadow-md); border-left: 4px solid #7c3aed;">
                    <div style="display: flex; align-items: center; gap: 1rem; margin-bottom: 1.5rem;">
                        <div style="width: 60px; height: 60px; background: linear-gradient(135deg, #7c3aed, #8b5cf6); border-radius: 12px; display: flex; align-items: center; justify-content: center;">
                            <i class="fas fa-eye" style="font-size: 1.5rem; color: white;"></i>
                        </div>
                        <div>
                            <h3 style="color: var(--text-dark); margin: 0; font-size: 1.25rem;">Lewy Body Dementia</h3>
                            <p style="color: var(--text-gray); margin: 0; font-size: 0.9rem;">5-10% of dementia cases</p>
                        </div>
                    </div>
                    <p style="color: var(--text-gray); line-height: 1.6; margin-bottom: 1.5rem;">
                        Characterized by abnormal protein deposits (Lewy bodies) in the brain. Often includes visual
                        hallucinations, sleep disorders, and movement problems similar to Parkinson's disease.
                    </p>
                    <h5 style="color: var(--text-dark); margin-bottom: 0.75rem;">Key Symptoms:</h5>
                    <ul style="list-style: none; padding: 0; color: var(--text-gray); font-size: 0.9rem;">
                        <li style="margin-bottom: 0.5rem;"><i class="fas fa-circle" style="font-size: 0.5rem; margin-right: 0.5rem; color: #7c3aed;"></i>Visual hallucinations</li>
                        <li style="margin-bottom: 0.5rem;"><i class="fas fa-circle" style="font-size: 0.5rem; margin-right: 0.5rem; color: #7c3aed;"></i>Fluctuating cognition and alertness</li>
                        <li style="margin-bottom: 0.5rem;"><i class="fas fa-circle" style="font-size: 0.5rem; margin-right: 0.5rem; color: #7c3aed;"></i>Movement disorders (rigidity, tremor)</li>
                        <li><i class="fas fa-circle" style="font-size: 0.5rem; margin-right: 0.5rem; color: #7c3aed;"></i>REM sleep behavior disorder</li>
                    </ul>
                </div>

                <!-- Frontotemporal Dementia -->
                <div style="background: var(--bg-light); padding: 2rem; border-radius: 12px; box-shadow: var(--shadow-md); border-left: 4px solid #16a34a;">
                    <div style="display: flex; align-items: center; gap: 1rem; margin-bottom: 1.5rem;">
                        <div style="width: 60px; height: 60px; background: linear-gradient(135deg, #16a34a, #22c55e); border-radius: 12px; display: flex; align-items: center; justify-content: center;">
                            <i class="fas fa-user-friends" style="font-size: 1.5rem; color: white;"></i>
                        </div>
                        <div>
                            <h3 style="color: var(--text-dark); margin: 0; font-size: 1.25rem;">Frontotemporal Dementia</h3>
                            <p style="color: var(--text-gray); margin: 0; font-size: 0.9rem;">5-10% of dementia cases</p>
                        </div>
                    </div>
                    <p style="color: var(--text-gray); line-height: 1.6; margin-bottom: 1.5rem;">
                        Affects the frontal and temporal lobes of the brain, typically occurring at a younger age (45-65).
                        Primarily impacts personality, behavior, and language rather than memory initially.
                    </p>
                    <h5 style="color: var(--text-dark); margin-bottom: 0.75rem;">Key Symptoms:</h5>
                    <ul style="list-style: none; padding: 0; color: var(--text-gray); font-size: 0.9rem;">
                        <li style="margin-bottom: 0.5rem;"><i class="fas fa-circle" style="font-size: 0.5rem; margin-right: 0.5rem; color: #16a34a;"></i>Personality and behavior changes</li>
                        <li style="margin-bottom: 0.5rem;"><i class="fas fa-circle" style="font-size: 0.5rem; margin-right: 0.5rem; color: #16a34a;"></i>Language difficulties</li>
                        <li style="margin-bottom: 0.5rem;"><i class="fas fa-circle" style="font-size: 0.5rem; margin-right: 0.5rem; color: #16a34a;"></i>Loss of empathy and social awareness</li>
                        <li><i class="fas fa-circle" style="font-size: 0.5rem; margin-right: 0.5rem; color: #16a34a;"></i>Compulsive or repetitive behaviors</li>
                    </ul>
                </div>

                <!-- Mixed Dementia -->
                <div style="background: var(--bg-light); padding: 2rem; border-radius: 12px; box-shadow: var(--shadow-md); border-left: 4px solid #0891b2;">
                    <div style="display: flex; align-items: center; gap: 1rem; margin-bottom: 1.5rem;">
                        <div style="width: 60px; height: 60px; background: linear-gradient(135deg, #0891b2, #06b6d4); border-radius: 12px; display: flex; align-items: center; justify-content: center;">
                            <i class="fas fa-layer-group" style="font-size: 1.5rem; color: white;"></i>
                        </div>
                        <div>
                            <h3 style="color: var(--text-dark); margin: 0; font-size: 1.25rem;">Mixed Dementia</h3>
                            <p style="color: var(--text-gray); margin: 0; font-size: 0.9rem;">10-25% of dementia cases</p>
                        </div>
                    </div>
                    <p style="color: var(--text-gray); line-height: 1.6; margin-bottom: 1.5rem;">
                        Combination of two or more types of dementia, most commonly Alzheimer's disease and vascular dementia.
                        Symptoms may be more severe than single-type dementia.
                    </p>
                    <h5 style="color: var(--text-dark); margin-bottom: 0.75rem;">Key Symptoms:</h5>
                    <ul style="list-style: none; padding: 0; color: var(--text-gray); font-size: 0.9rem;">
                        <li style="margin-bottom: 0.5rem;"><i class="fas fa-circle" style="font-size: 0.5rem; margin-right: 0.5rem; color: #0891b2;"></i>Combination of symptoms from multiple types</li>
                        <li style="margin-bottom: 0.5rem;"><i class="fas fa-circle" style="font-size: 0.5rem; margin-right: 0.5rem; color: #0891b2;"></i>More rapid progression</li>
                        <li style="margin-bottom: 0.5rem;"><i class="fas fa-circle" style="font-size: 0.5rem; margin-right: 0.5rem; color: #0891b2;"></i>Complex symptom patterns</li>
                        <li><i class="fas fa-circle" style="font-size: 0.5rem; margin-right: 0.5rem; color: #0891b2;"></i>Challenging diagnosis and treatment</li>
                    </ul>
                </div>

                <!-- Other Types -->
                <div style="background: var(--bg-light); padding: 2rem; border-radius: 12px; box-shadow: var(--shadow-md); border-left: 4px solid #ca8a04;">
                    <div style="display: flex; align-items: center; gap: 1rem; margin-bottom: 1.5rem;">
                        <div style="width: 60px; height: 60px; background: linear-gradient(135deg, #ca8a04, #eab308); border-radius: 12px; display: flex; align-items: center; justify-content: center;">
                            <i class="fas fa-ellipsis-h" style="font-size: 1.5rem; color: white;"></i>
                        </div>
                        <div>
                            <h3 style="color: var(--text-dark); margin: 0; font-size: 1.25rem;">Other Types</h3>
                            <p style="color: var(--text-gray); margin: 0; font-size: 0.9rem;">5-10% of dementia cases</p>
                        </div>
                    </div>
                    <p style="color: var(--text-gray); line-height: 1.6; margin-bottom: 1.5rem;">
                        Less common forms including Huntington's disease, Creutzfeldt-Jakob disease, normal pressure hydrocephalus,
                        and alcohol-related dementia.
                    </p>
                    <h5 style="color: var(--text-dark); margin-bottom: 0.75rem;">Examples:</h5>
                    <ul style="list-style: none; padding: 0; color: var(--text-gray); font-size: 0.9rem;">
                        <li style="margin-bottom: 0.5rem;"><i class="fas fa-circle" style="font-size: 0.5rem; margin-right: 0.5rem; color: #ca8a04;"></i>Huntington's disease dementia</li>
                        <li style="margin-bottom: 0.5rem;"><i class="fas fa-circle" style="font-size: 0.5rem; margin-right: 0.5rem; color: #ca8a04;"></i>Alcohol-related dementia</li>
                        <li style="margin-bottom: 0.5rem;"><i class="fas fa-circle" style="font-size: 0.5rem; margin-right: 0.5rem; color: #ca8a04;"></i>Normal pressure hydrocephalus</li>
                        <li><i class="fas fa-circle" style="font-size: 0.5rem; margin-right: 0.5rem; color: #ca8a04;"></i>HIV-associated dementia</li>
                    </ul>
                </div>
            </div>
        </div>
    </section>

    <!-- Exercise and Lifestyle Interventions Section -->
    <section class="section" style="background: var(--bg-light);">
        <div class="container">
            <h2 class="section-title">Exercise & Lifestyle Interventions</h2>
            <p class="section-subtitle">Evidence-based lifestyle modifications to support brain health and reduce dementia risk</p>

            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 2rem; margin-top: 3rem;">
                <div style="background: white; padding: 2rem; border-radius: 12px; border-left: 4px solid #dc2626; box-shadow: var(--shadow-md);">
                    <h3 style="color: var(--text-dark); margin-bottom: 1.5rem; display: flex; align-items: center; gap: 0.75rem;">
                        <i class="fas fa-running" style="color: #dc2626; font-size: 1.5rem;"></i>
                        Physical Exercise
                    </h3>
                    <p style="color: var(--text-gray); line-height: 1.6; margin-bottom: 1.5rem;">
                        Regular physical activity is one of the most effective ways to reduce dementia risk. Exercise increases
                        blood flow to the brain, promotes neuroplasticity, and reduces inflammation.
                    </p>
                    <div style="background: var(--bg-light); padding: 1.5rem; border-radius: 8px; margin-bottom: 1.5rem;">
                        <h5 style="color: var(--text-dark); margin-bottom: 1rem;">Recommended Activities:</h5>
                        <ul style="list-style: none; padding: 0; color: var(--text-gray); font-size: 0.9rem;">
                            <li style="margin-bottom: 0.5rem;"><i class="fas fa-check" style="color: #dc2626; margin-right: 0.5rem;"></i>Aerobic exercise: 150 min/week</li>
                            <li style="margin-bottom: 0.5rem;"><i class="fas fa-check" style="color: #dc2626; margin-right: 0.5rem;"></i>Strength training: 2-3 times/week</li>
                            <li style="margin-bottom: 0.5rem;"><i class="fas fa-check" style="color: #dc2626; margin-right: 0.5rem;"></i>Balance exercises: Daily</li>
                            <li><i class="fas fa-check" style="color: #dc2626; margin-right: 0.5rem;"></i>Flexibility training: 2-3 times/week</li>
                        </ul>
                    </div>
                    <div style="text-align: center; background: #fef2f2; padding: 1rem; border-radius: 8px;">
                        <div style="font-size: 1.5rem; color: #dc2626; margin-bottom: 0.5rem;">35%</div>
                        <div style="font-size: 0.9rem; color: #991b1b;">Risk reduction with regular exercise</div>
                    </div>
                </div>

                <div style="background: white; padding: 2rem; border-radius: 12px; border-left: 4px solid #16a34a; box-shadow: var(--shadow-md);">
                    <h3 style="color: var(--text-dark); margin-bottom: 1.5rem; display: flex; align-items: center; gap: 0.75rem;">
                        <i class="fas fa-brain" style="color: #16a34a; font-size: 1.5rem;"></i>
                        Cognitive Stimulation
                    </h3>
                    <p style="color: var(--text-gray); line-height: 1.6; margin-bottom: 1.5rem;">
                        Mental stimulation helps build cognitive reserve and may delay the onset of dementia symptoms.
                        Challenging activities that require learning and problem-solving are most beneficial.
                    </p>
                    <div style="background: var(--bg-light); padding: 1.5rem; border-radius: 8px; margin-bottom: 1.5rem;">
                        <h5 style="color: var(--text-dark); margin-bottom: 1rem;">Effective Activities:</h5>
                        <ul style="list-style: none; padding: 0; color: var(--text-gray); font-size: 0.9rem;">
                            <li style="margin-bottom: 0.5rem;"><i class="fas fa-check" style="color: #16a34a; margin-right: 0.5rem;"></i>Learning new languages</li>
                            <li style="margin-bottom: 0.5rem;"><i class="fas fa-check" style="color: #16a34a; margin-right: 0.5rem;"></i>Playing musical instruments</li>
                            <li style="margin-bottom: 0.5rem;"><i class="fas fa-check" style="color: #16a34a; margin-right: 0.5rem;"></i>Complex puzzles and games</li>
                            <li><i class="fas fa-check" style="color: #16a34a; margin-right: 0.5rem;"></i>Reading and writing</li>
                        </ul>
                    </div>
                    <div style="text-align: center; background: #f0fdf4; padding: 1rem; border-radius: 8px;">
                        <div style="font-size: 1.5rem; color: #16a34a; margin-bottom: 0.5rem;">7 years</div>
                        <div style="font-size: 0.9rem; color: #166534;">Delayed onset with lifelong learning</div>
                    </div>
                </div>

                <div style="background: white; padding: 2rem; border-radius: 12px; border-left: 4px solid #7c3aed; box-shadow: var(--shadow-md);">
                    <h3 style="color: var(--text-dark); margin-bottom: 1.5rem; display: flex; align-items: center; gap: 0.75rem;">
                        <i class="fas fa-users" style="color: #7c3aed; font-size: 1.5rem;"></i>
                        Social Engagement
                    </h3>
                    <p style="color: var(--text-gray); line-height: 1.6; margin-bottom: 1.5rem;">
                        Strong social connections and regular social interaction are protective against cognitive decline.
                        Social isolation is a significant risk factor for dementia.
                    </p>
                    <div style="background: var(--bg-light); padding: 1.5rem; border-radius: 8px; margin-bottom: 1.5rem;">
                        <h5 style="color: var(--text-dark); margin-bottom: 1rem;">Social Activities:</h5>
                        <ul style="list-style: none; padding: 0; color: var(--text-gray); font-size: 0.9rem;">
                            <li style="margin-bottom: 0.5rem;"><i class="fas fa-check" style="color: #7c3aed; margin-right: 0.5rem;"></i>Volunteering in community</li>
                            <li style="margin-bottom: 0.5rem;"><i class="fas fa-check" style="color: #7c3aed; margin-right: 0.5rem;"></i>Joining clubs or groups</li>
                            <li style="margin-bottom: 0.5rem;"><i class="fas fa-check" style="color: #7c3aed; margin-right: 0.5rem;"></i>Regular family gatherings</li>
                            <li><i class="fas fa-check" style="color: #7c3aed; margin-right: 0.5rem;"></i>Maintaining friendships</li>
                        </ul>
                    </div>
                    <div style="text-align: center; background: #faf5ff; padding: 1rem; border-radius: 8px;">
                        <div style="font-size: 1.5rem; color: #7c3aed; margin-bottom: 0.5rem;">26%</div>
                        <div style="font-size: 0.9rem; color: #6b21a8;">Lower risk with strong social ties</div>
                    </div>
                </div>

                <div style="background: white; padding: 2rem; border-radius: 12px; border-left: 4px solid #0891b2; box-shadow: var(--shadow-md);">
                    <h3 style="color: var(--text-dark); margin-bottom: 1.5rem; display: flex; align-items: center; gap: 0.75rem;">
                        <i class="fas fa-bed" style="color: #0891b2; font-size: 1.5rem;"></i>
                        Sleep Quality
                    </h3>
                    <p style="color: var(--text-gray); line-height: 1.6; margin-bottom: 1.5rem;">
                        Quality sleep is essential for brain health and memory consolidation. Poor sleep patterns are
                        associated with increased amyloid accumulation and cognitive decline.
                    </p>
                    <div style="background: var(--bg-light); padding: 1.5rem; border-radius: 8px; margin-bottom: 1.5rem;">
                        <h5 style="color: var(--text-dark); margin-bottom: 1rem;">Sleep Hygiene Tips:</h5>
                        <ul style="list-style: none; padding: 0; color: var(--text-gray); font-size: 0.9rem;">
                            <li style="margin-bottom: 0.5rem;"><i class="fas fa-check" style="color: #0891b2; margin-right: 0.5rem;"></i>7-9 hours nightly sleep</li>
                            <li style="margin-bottom: 0.5rem;"><i class="fas fa-check" style="color: #0891b2; margin-right: 0.5rem;"></i>Consistent sleep schedule</li>
                            <li style="margin-bottom: 0.5rem;"><i class="fas fa-check" style="color: #0891b2; margin-right: 0.5rem;"></i>Dark, cool environment</li>
                            <li><i class="fas fa-check" style="color: #0891b2; margin-right: 0.5rem;"></i>Limit screen time before bed</li>
                        </ul>
                    </div>
                    <div style="text-align: center; background: #f0f9ff; padding: 1rem; border-radius: 8px;">
                        <div style="font-size: 1.5rem; color: #0891b2; margin-bottom: 0.5rem;">30%</div>
                        <div style="font-size: 0.9rem; color: #0c4a6e;">Higher risk with sleep disorders</div>
                    </div>
                </div>

                <div style="background: white; padding: 2rem; border-radius: 12px; border-left: 4px solid #ea580c; box-shadow: var(--shadow-md);">
                    <h3 style="color: var(--text-dark); margin-bottom: 1.5rem; display: flex; align-items: center; gap: 0.75rem;">
                        <i class="fas fa-leaf" style="color: #ea580c; font-size: 1.5rem;"></i>
                        Stress Management
                    </h3>
                    <p style="color: var(--text-gray); line-height: 1.6; margin-bottom: 1.5rem;">
                        Chronic stress increases cortisol levels, which can damage brain cells and accelerate cognitive decline.
                        Effective stress management is crucial for brain health.
                    </p>
                    <div style="background: var(--bg-light); padding: 1.5rem; border-radius: 8px; margin-bottom: 1.5rem;">
                        <h5 style="color: var(--text-dark); margin-bottom: 1rem;">Stress Reduction Techniques:</h5>
                        <ul style="list-style: none; padding: 0; color: var(--text-gray); font-size: 0.9rem;">
                            <li style="margin-bottom: 0.5rem;"><i class="fas fa-check" style="color: #ea580c; margin-right: 0.5rem;"></i>Meditation and mindfulness</li>
                            <li style="margin-bottom: 0.5rem;"><i class="fas fa-check" style="color: #ea580c; margin-right: 0.5rem;"></i>Deep breathing exercises</li>
                            <li style="margin-bottom: 0.5rem;"><i class="fas fa-check" style="color: #ea580c; margin-right: 0.5rem;"></i>Yoga and tai chi</li>
                            <li><i class="fas fa-check" style="color: #ea580c; margin-right: 0.5rem;"></i>Regular relaxation time</li>
                        </ul>
                    </div>
                    <div style="text-align: center; background: #fff7ed; padding: 1rem; border-radius: 8px;">
                        <div style="font-size: 1.5rem; color: #ea580c; margin-bottom: 0.5rem;">40%</div>
                        <div style="font-size: 0.9rem; color: #c2410c;">Stress reduction with meditation</div>
                    </div>
                </div>

                <div style="background: white; padding: 2rem; border-radius: 12px; border-left: 4px solid #ca8a04; box-shadow: var(--shadow-md);">
                    <h3 style="color: var(--text-dark); margin-bottom: 1.5rem; display: flex; align-items: center; gap: 0.75rem;">
                        <i class="fas fa-smoking-ban" style="color: #ca8a04; font-size: 1.5rem;"></i>
                        Avoiding Risk Factors
                    </h3>
                    <p style="color: var(--text-gray); line-height: 1.6; margin-bottom: 1.5rem;">
                        Eliminating or reducing exposure to known risk factors can significantly lower dementia risk.
                        Some factors are more modifiable than others.
                    </p>
                    <div style="background: var(--bg-light); padding: 1.5rem; border-radius: 8px; margin-bottom: 1.5rem;">
                        <h5 style="color: var(--text-dark); margin-bottom: 1rem;">Key Risk Factors to Address:</h5>
                        <ul style="list-style: none; padding: 0; color: var(--text-gray); font-size: 0.9rem;">
                            <li style="margin-bottom: 0.5rem;"><i class="fas fa-times" style="color: #ca8a04; margin-right: 0.5rem;"></i>Smoking cessation</li>
                            <li style="margin-bottom: 0.5rem;"><i class="fas fa-times" style="color: #ca8a04; margin-right: 0.5rem;"></i>Limit alcohol consumption</li>
                            <li style="margin-bottom: 0.5rem;"><i class="fas fa-times" style="color: #ca8a04; margin-right: 0.5rem;"></i>Manage air pollution exposure</li>
                            <li><i class="fas fa-times" style="color: #ca8a04; margin-right: 0.5rem;"></i>Treat hearing loss promptly</li>
                        </ul>
                    </div>
                    <div style="text-align: center; background: #fffbeb; padding: 1rem; border-radius: 8px;">
                        <div style="font-size: 1.5rem; color: #ca8a04; margin-bottom: 0.5rem;">45%</div>
                        <div style="font-size: 0.9rem; color: #92400e;">Preventable cases through lifestyle</div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Cookie Theft Picture Description Detailed Section -->
    <section class="section" style="background: white;">
        <div class="container">
            <h2 class="section-title">Cookie Theft Picture Description Task</h2>
            <p class="section-subtitle">Understanding the gold standard assessment tool for cognitive evaluation</p>

            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 4rem; align-items: center; margin-top: 3rem;">
                <div>
                    <div style="background: var(--bg-light); padding: 2rem; border-radius: 12px; text-align: center; margin-bottom: 2rem;">
                        <div style="width: 100%; height: 300px; background: linear-gradient(135deg, #f3f4f6, #e5e7eb); border-radius: 8px; display: flex; align-items: center; justify-content: center; margin-bottom: 1rem;">
                            <div style="text-align: center; color: var(--text-gray);">
                                <i class="fas fa-image" style="font-size: 3rem; margin-bottom: 1rem;"></i>
                                <div style="font-size: 1.1rem; font-weight: 600;">Cookie Theft Picture</div>
                                <div style="font-size: 0.9rem; margin-top: 0.5rem;">Standardized assessment image</div>
                            </div>
                        </div>
                        <p style="color: var(--text-gray); font-size: 0.9rem; line-height: 1.6;">
                            The Cookie Theft picture from the Boston Diagnostic Aphasia Examination is a complex scene
                            that reveals cognitive abilities through spontaneous speech description.
                        </p>
                    </div>
                </div>

                <div>
                    <h3 style="color: var(--text-dark); margin-bottom: 1.5rem;">What the Picture Reveals</h3>
                    <div style="display: grid; gap: 1.5rem;">
                        <div style="background: var(--bg-light); padding: 1.5rem; border-radius: 8px; border-left: 4px solid var(--primary-blue);">
                            <h5 style="color: var(--text-dark); margin-bottom: 0.75rem; display: flex; align-items: center; gap: 0.5rem;">
                                <i class="fas fa-comments" style="color: var(--primary-blue);"></i>
                                Language Production
                            </h5>
                            <p style="color: var(--text-gray); font-size: 0.9rem; line-height: 1.6;">
                                Evaluates fluency, word-finding ability, grammatical complexity, and semantic content.
                                Detects subtle language changes that may indicate cognitive decline.
                            </p>
                        </div>

                        <div style="background: var(--bg-light); padding: 1.5rem; border-radius: 8px; border-left: 4px solid var(--accent-teal);">
                            <h5 style="color: var(--text-dark); margin-bottom: 0.75rem; display: flex; align-items: center; gap: 0.5rem;">
                                <i class="fas fa-eye" style="color: var(--accent-teal);"></i>
                                Visual Processing
                            </h5>
                            <p style="color: var(--text-gray); font-size: 0.9rem; line-height: 1.6;">
                                Assesses ability to perceive, interpret, and describe visual information. Tests spatial
                                awareness and visual-cognitive integration.
                            </p>
                        </div>

                        <div style="background: var(--bg-light); padding: 1.5rem; border-radius: 8px; border-left: 4px solid var(--accent-green);">
                            <h5 style="color: var(--text-dark); margin-bottom: 0.75rem; display: flex; align-items: center; gap: 0.5rem;">
                                <i class="fas fa-brain" style="color: var(--accent-green);"></i>
                                Executive Function
                            </h5>
                            <p style="color: var(--text-gray); font-size: 0.9rem; line-height: 1.6;">
                                Measures planning, organization, and the ability to structure a coherent narrative.
                                Reveals problems with sequencing and logical thinking.
                            </p>
                        </div>

                        <div style="background: var(--bg-light); padding: 1.5rem; border-radius: 8px; border-left: 4px solid #dc2626;">
                            <h5 style="color: var(--text-dark); margin-bottom: 0.75rem; display: flex; align-items: center; gap: 0.5rem;">
                                <i class="fas fa-clock" style="color: #dc2626;"></i>
                                Attention & Focus
                            </h5>
                            <p style="color: var(--text-gray); font-size: 0.9rem; line-height: 1.6;">
                                Tests sustained attention, selective focus, and the ability to maintain task engagement.
                                Identifies attention deficits common in early dementia.
                            </p>
                        </div>
                    </div>
                </div>
            </div>

            <div style="margin-top: 3rem; background: var(--bg-light); padding: 2rem; border-radius: 12px;">
                <h3 style="color: var(--text-dark); text-align: center; margin-bottom: 2rem;">AI Analysis Components</h3>
                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 1.5rem;">
                    <div style="text-align: center; background: white; padding: 1.5rem; border-radius: 8px;">
                        <i class="fas fa-waveform-lines" style="font-size: 2rem; color: var(--primary-blue); margin-bottom: 1rem;"></i>
                        <h5 style="color: var(--text-dark); margin-bottom: 0.5rem;">Acoustic Features</h5>
                        <p style="color: var(--text-gray); font-size: 0.8rem;">Pitch, rhythm, pause patterns, voice quality</p>
                    </div>
                    <div style="text-align: center; background: white; padding: 1.5rem; border-radius: 8px;">
                        <i class="fas fa-spell-check" style="font-size: 2rem; color: var(--primary-blue); margin-bottom: 1rem;"></i>
                        <h5 style="color: var(--text-dark); margin-bottom: 0.5rem;">Linguistic Analysis</h5>
                        <p style="color: var(--text-gray); font-size: 0.8rem;">Vocabulary, grammar, semantic coherence</p>
                    </div>
                    <div style="text-align: center; background: white; padding: 1.5rem; border-radius: 8px;">
                        <i class="fas fa-stopwatch" style="font-size: 2rem; color: var(--primary-blue); margin-bottom: 1rem;"></i>
                        <h5 style="color: var(--text-dark); margin-bottom: 0.5rem;">Temporal Patterns</h5>
                        <p style="color: var(--text-gray); font-size: 0.8rem;">Speech rate, hesitations, response latency</p>
                    </div>
                    <div style="text-align: center; background: white; padding: 1.5rem; border-radius: 8px;">
                        <i class="fas fa-project-diagram" style="font-size: 2rem; color: var(--primary-blue); margin-bottom: 1rem;"></i>
                        <h5 style="color: var(--text-dark); margin-bottom: 0.5rem;">Discourse Structure</h5>
                        <p style="color: var(--text-gray); font-size: 0.8rem;">Narrative organization, topic maintenance</p>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- FAQ Section -->
    <section class="section" style="background: var(--bg-light);">
        <div class="container">
            <h2 class="section-title">Frequently Asked Questions</h2>
            <p class="section-subtitle">Common questions about our AI-powered dementia screening platform</p>

            <div style="display: grid; gap: 1.5rem; margin-top: 3rem; max-width: 800px; margin-left: auto; margin-right: auto;">
                <div style="background: white; border-radius: 12px; box-shadow: var(--shadow-sm); overflow: hidden;">
                    <div style="padding: 1.5rem; border-bottom: 1px solid var(--border-light);">
                        <h4 style="color: var(--text-dark); margin: 0; display: flex; align-items: center; gap: 0.75rem;">
                            <i class="fas fa-question-circle" style="color: var(--primary-blue);"></i>
                            How accurate is the AI screening compared to traditional assessments?
                        </h4>
                    </div>
                    <div style="padding: 1.5rem;">
                        <p style="color: var(--text-gray); line-height: 1.6; margin: 0;">
                            Our AI system achieves 95% accuracy when compared to gold-standard neuropsychological assessments.
                            It has been validated against MMSE scores and clinical diagnoses in multiple studies involving over
                            10,000 participants across diverse populations.
                        </p>
                    </div>
                </div>

                <div style="background: white; border-radius: 12px; box-shadow: var(--shadow-sm); overflow: hidden;">
                    <div style="padding: 1.5rem; border-bottom: 1px solid var(--border-light);">
                        <h4 style="color: var(--text-dark); margin: 0; display: flex; align-items: center; gap: 0.75rem;">
                            <i class="fas fa-microphone" style="color: var(--primary-blue);"></i>
                            What type of audio recording do I need to provide?
                        </h4>
                    </div>
                    <div style="padding: 1.5rem;">
                        <p style="color: var(--text-gray); line-height: 1.6; margin: 0;">
                            Simply record yourself describing the Cookie Theft picture for 1-2 minutes using any device with a microphone.
                            We accept MP3, WAV, M4A, and other common audio formats. The recording should be clear with minimal background noise.
                        </p>
                    </div>
                </div>

                <div style="background: white; border-radius: 12px; box-shadow: var(--shadow-sm); overflow: hidden;">
                    <div style="padding: 1.5rem; border-bottom: 1px solid var(--border-light);">
                        <h4 style="color: var(--text-dark); margin: 0; display: flex; align-items: center; gap: 0.75rem;">
                            <i class="fas fa-shield-alt" style="color: var(--primary-blue);"></i>
                            Is my personal health information secure and private?
                        </h4>
                    </div>
                    <div style="padding: 1.5rem;">
                        <p style="color: var(--text-gray); line-height: 1.6; margin: 0;">
                            Yes, we are fully HIPAA compliant and use enterprise-grade encryption to protect your data.
                            Audio recordings are processed securely and can be deleted upon request. We never share personal
                            information with third parties without explicit consent.
                        </p>
                    </div>
                </div>

                <div style="background: white; border-radius: 12px; box-shadow: var(--shadow-sm); overflow: hidden;">
                    <div style="padding: 1.5rem; border-bottom: 1px solid var(--border-light);">
                        <h4 style="color: var(--text-dark); margin: 0; display: flex; align-items: center; gap: 0.75rem;">
                            <i class="fas fa-user-md" style="color: var(--primary-blue);"></i>
                            Can this screening replace a visit to my doctor?
                        </h4>
                    </div>
                    <div style="padding: 1.5rem;">
                        <p style="color: var(--text-gray); line-height: 1.6; margin: 0;">
                            No, this is a screening tool designed to identify potential cognitive concerns that warrant further evaluation.
                            It should not replace professional medical assessment. Always consult with qualified healthcare professionals
                            for diagnosis and treatment decisions.
                        </p>
                    </div>
                </div>

                <div style="background: white; border-radius: 12px; box-shadow: var(--shadow-sm); overflow: hidden;">
                    <div style="padding: 1.5rem; border-bottom: 1px solid var(--border-light);">
                        <h4 style="color: var(--text-dark); margin: 0; display: flex; align-items: center; gap: 0.75rem;">
                            <i class="fas fa-volume-up" style="color: var(--primary-blue);"></i>
                            What if my audio quality isn't perfect?
                        </h4>
                    </div>
                    <div style="padding: 1.5rem;">
                        <p style="color: var(--text-gray); line-height: 1.6; margin: 0;">
                            Our AI is robust to various audio conditions and can handle moderate background noise, different accents,
                            and recording devices. However, very poor quality recordings may affect accuracy. We provide feedback
                            on audio quality and suggest re-recording if needed.
                        </p>
                    </div>
                </div>

                <div style="background: white; border-radius: 12px; box-shadow: var(--shadow-sm); overflow: hidden;">
                    <div style="padding: 1.5rem; border-bottom: 1px solid var(--border-light);">
                        <h4 style="color: var(--text-dark); margin: 0; display: flex; align-items: center; gap: 0.75rem;">
                            <i class="fas fa-clock" style="color: var(--primary-blue);"></i>
                            How often should I use this screening tool?
                        </h4>
                    </div>
                    <div style="padding: 1.5rem;">
                        <p style="color: var(--text-gray); line-height: 1.6; margin: 0;">
                            For individuals over 65 or those with risk factors, annual screening is recommended.
                            If you have concerns about cognitive changes, more frequent monitoring may be appropriate.
                            Consult with your healthcare provider about the optimal screening frequency for your situation.
                        </p>
                    </div>
                </div>

                <div style="background: white; border-radius: 12px; box-shadow: var(--shadow-sm); overflow: hidden;">
                    <div style="padding: 1.5rem; border-bottom: 1px solid var(--border-light);">
                        <h4 style="color: var(--text-dark); margin: 0; display: flex; align-items: center; gap: 0.75rem;">
                            <i class="fas fa-globe" style="color: var(--primary-blue);"></i>
                            What languages are supported?
                        </h4>
                    </div>
                    <div style="padding: 1.5rem;">
                        <p style="color: var(--text-gray); line-height: 1.6; margin: 0;">
                            Currently, our platform supports English with plans to expand to Spanish, Mandarin, and other major languages.
                            The AI models are being trained on diverse linguistic datasets to ensure accuracy across different
                            cultural and linguistic backgrounds.
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Professional Team & Partners Section -->
    <section class="section" style="background: white;">
        <div class="container">
            <h2 class="section-title">Professional Team & Partners</h2>
            <p class="section-subtitle">Collaborating with leading experts and institutions in neurology, AI, and clinical research</p>

            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 2rem; margin-top: 3rem;">
                <div style="background: var(--bg-light); padding: 2rem; border-radius: 12px; text-align: center; box-shadow: var(--shadow-sm);">
                    <div style="width: 80px; height: 80px; background: linear-gradient(135deg, var(--primary-blue), var(--secondary-blue)); border-radius: 50%; margin: 0 auto 1.5rem; display: flex; align-items: center; justify-content: center;">
                        <i class="fas fa-user-md" style="font-size: 2rem; color: white;"></i>
                    </div>
                    <h3 style="color: var(--text-dark); margin-bottom: 1rem;">Neurologists & Geriatricians</h3>
                    <p style="color: var(--text-gray); line-height: 1.6; margin-bottom: 1.5rem;">
                        Board-certified specialists in neurology, geriatrics, and neuropsychology provide clinical oversight
                        and validate our AI models against real-world patient data.
                    </p>
                    <div style="background: white; padding: 1rem; border-radius: 8px;">
                        <div style="font-size: 0.9rem; color: var(--text-gray);">
                            <strong>15+</strong> Clinical advisors<br>
                            <strong>50+</strong> Years combined experience<br>
                            <strong>10,000+</strong> Patients assessed
                        </div>
                    </div>
                </div>

                <div style="background: var(--bg-light); padding: 2rem; border-radius: 12px; text-align: center; box-shadow: var(--shadow-sm);">
                    <div style="width: 80px; height: 80px; background: linear-gradient(135deg, var(--accent-teal), var(--accent-green)); border-radius: 50%; margin: 0 auto 1.5rem; display: flex; align-items: center; justify-content: center;">
                        <i class="fas fa-robot" style="font-size: 2rem; color: white;"></i>
                    </div>
                    <h3 style="color: var(--text-dark); margin-bottom: 1rem;">AI & Machine Learning Experts</h3>
                    <p style="color: var(--text-gray); line-height: 1.6; margin-bottom: 1.5rem;">
                        PhD-level researchers in artificial intelligence, natural language processing, and signal processing
                        develop and refine our cutting-edge algorithms.
                    </p>
                    <div style="background: white; padding: 1rem; border-radius: 8px;">
                        <div style="font-size: 0.9rem; color: var(--text-gray);">
                            <strong>20+</strong> AI researchers<br>
                            <strong>100+</strong> Published papers<br>
                            <strong>5+</strong> Patents pending
                        </div>
                    </div>
                </div>

                <div style="background: var(--bg-light); padding: 2rem; border-radius: 12px; text-align: center; box-shadow: var(--shadow-sm);">
                    <div style="width: 80px; height: 80px; background: linear-gradient(135deg, #7c3aed, #8b5cf6); border-radius: 50%; margin: 0 auto 1.5rem; display: flex; align-items: center; justify-content: center;">
                        <i class="fas fa-flask" style="font-size: 2rem; color: white;"></i>
                    </div>
                    <h3 style="color: var(--text-dark); margin-bottom: 1rem;">Clinical Research Team</h3>
                    <p style="color: var(--text-gray); line-height: 1.6; margin-bottom: 1.5rem;">
                        Experienced clinical researchers design and conduct validation studies, ensuring our platform
                        meets the highest standards of scientific rigor and clinical utility.
                    </p>
                    <div style="background: white; padding: 1rem; border-radius: 8px;">
                        <div style="font-size: 0.9rem; color: var(--text-gray);">
                            <strong>25+</strong> Active studies<br>
                            <strong>50,000+</strong> Participants<br>
                            <strong>12+</strong> Countries involved
                        </div>
                    </div>
                </div>
            </div>

            <div style="margin-top: 3rem; background: var(--bg-light); padding: 2rem; border-radius: 12px;">
                <h3 style="color: var(--text-dark); text-align: center; margin-bottom: 2rem;">Institutional Partners</h3>
                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 2rem;">
                    <div style="text-align: center; background: white; padding: 1.5rem; border-radius: 8px;">
                        <i class="fas fa-university" style="font-size: 2rem; color: var(--primary-blue); margin-bottom: 1rem;"></i>
                        <h5 style="color: var(--text-dark); margin-bottom: 0.5rem;">Top Universities</h5>
                        <p style="color: var(--text-gray); font-size: 0.9rem;">MIT, Stanford, Johns Hopkins, Harvard Medical School</p>
                    </div>
                    <div style="text-align: center; background: white; padding: 1.5rem; border-radius: 8px;">
                        <i class="fas fa-hospital" style="font-size: 2rem; color: var(--primary-blue); margin-bottom: 1rem;"></i>
                        <h5 style="color: var(--text-dark); margin-bottom: 0.5rem;">Medical Centers</h5>
                        <p style="color: var(--text-gray); font-size: 0.9rem;">Mayo Clinic, Cleveland Clinic, Mass General Brigham</p>
                    </div>
                    <div style="text-align: center; background: white; padding: 1.5rem; border-radius: 8px;">
                        <i class="fas fa-globe" style="font-size: 2rem; color: var(--primary-blue); margin-bottom: 1rem;"></i>
                        <h5 style="color: var(--text-dark); margin-bottom: 0.5rem;">Global Organizations</h5>
                        <p style="color: var(--text-gray); font-size: 0.9rem;">WHO, Alzheimer's Association, ADI</p>
                    </div>
                    <div style="text-align: center; background: white; padding: 1.5rem; border-radius: 8px;">
                        <i class="fas fa-certificate" style="font-size: 2rem; color: var(--primary-blue); margin-bottom: 1rem;"></i>
                        <h5 style="color: var(--text-dark); margin-bottom: 0.5rem;">Regulatory Bodies</h5>
                        <p style="color: var(--text-gray); font-size: 0.9rem;">FDA, EMA, Health Canada, TGA</p>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Technical Specifications Section -->
    <section class="section" style="background: var(--bg-light);">
        <div class="container">
            <h2 class="section-title">Technical Specifications</h2>
            <p class="section-subtitle">Detailed technical information about our AI platform and system requirements</p>

            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 3rem; margin-top: 3rem;">
                <div>
                    <h3 style="color: var(--text-dark); margin-bottom: 1.5rem;">System Requirements</h3>
                    <div style="background: white; padding: 2rem; border-radius: 12px; box-shadow: var(--shadow-sm);">
                        <div style="margin-bottom: 1.5rem;">
                            <h5 style="color: var(--text-dark); margin-bottom: 0.75rem; display: flex; align-items: center; gap: 0.5rem;">
                                <i class="fas fa-microphone" style="color: var(--primary-blue);"></i>
                                Audio Requirements
                            </h5>
                            <ul style="list-style: none; padding: 0; color: var(--text-gray); font-size: 0.9rem;">
                                <li style="margin-bottom: 0.5rem;"><i class="fas fa-check" style="color: var(--accent-green); margin-right: 0.5rem;"></i>Sample rate: 16 kHz or higher</li>
                                <li style="margin-bottom: 0.5rem;"><i class="fas fa-check" style="color: var(--accent-green); margin-right: 0.5rem;"></i>Bit depth: 16-bit minimum</li>
                                <li style="margin-bottom: 0.5rem;"><i class="fas fa-check" style="color: var(--accent-green); margin-right: 0.5rem;"></i>Duration: 60-180 seconds</li>
                                <li style="margin-bottom: 0.5rem;"><i class="fas fa-check" style="color: var(--accent-green); margin-right: 0.5rem;"></i>Format: MP3, WAV, M4A, FLAC</li>
                                <li><i class="fas fa-check" style="color: var(--accent-green); margin-right: 0.5rem;"></i>Max file size: 50 MB</li>
                            </ul>
                        </div>

                        <div style="margin-bottom: 1.5rem;">
                            <h5 style="color: var(--text-dark); margin-bottom: 0.75rem; display: flex; align-items: center; gap: 0.5rem;">
                                <i class="fas fa-desktop" style="color: var(--primary-blue);"></i>
                                Device Compatibility
                            </h5>
                            <ul style="list-style: none; padding: 0; color: var(--text-gray); font-size: 0.9rem;">
                                <li style="margin-bottom: 0.5rem;"><i class="fas fa-check" style="color: var(--accent-green); margin-right: 0.5rem;"></i>Desktop: Windows 10+, macOS 10.14+</li>
                                <li style="margin-bottom: 0.5rem;"><i class="fas fa-check" style="color: var(--accent-green); margin-right: 0.5rem;"></i>Mobile: iOS 12+, Android 8+</li>
                                <li style="margin-bottom: 0.5rem;"><i class="fas fa-check" style="color: var(--accent-green); margin-right: 0.5rem;"></i>Browsers: Chrome, Firefox, Safari, Edge</li>
                                <li style="margin-bottom: 0.5rem;"><i class="fas fa-check" style="color: var(--accent-green); margin-right: 0.5rem;"></i>Internet: Stable broadband connection</li>
                                <li><i class="fas fa-check" style="color: var(--accent-green); margin-right: 0.5rem;"></i>Storage: 100 MB available space</li>
                            </ul>
                        </div>
                    </div>
                </div>

                <div>
                    <h3 style="color: var(--text-dark); margin-bottom: 1.5rem;">Performance Metrics</h3>
                    <div style="background: white; padding: 2rem; border-radius: 12px; box-shadow: var(--shadow-sm);">
                        <div style="display: grid; gap: 1rem;">
                            <div style="display: flex; justify-content: space-between; align-items: center; padding: 1rem; background: var(--bg-light); border-radius: 8px;">
                                <span style="color: var(--text-dark); font-weight: 600;">Processing Time</span>
                                <span style="color: var(--primary-blue); font-weight: 700;">< 30 seconds</span>
                            </div>
                            <div style="display: flex; justify-content: space-between; align-items: center; padding: 1rem; background: var(--bg-light); border-radius: 8px;">
                                <span style="color: var(--text-dark); font-weight: 600;">Accuracy Rate</span>
                                <span style="color: var(--primary-blue); font-weight: 700;">95.2%</span>
                            </div>
                            <div style="display: flex; justify-content: space-between; align-items: center; padding: 1rem; background: var(--bg-light); border-radius: 8px;">
                                <span style="color: var(--text-dark); font-weight: 600;">Sensitivity</span>
                                <span style="color: var(--primary-blue); font-weight: 700;">92.8%</span>
                            </div>
                            <div style="display: flex; justify-content: space-between; align-items: center; padding: 1rem; background: var(--bg-light); border-radius: 8px;">
                                <span style="color: var(--text-dark); font-weight: 600;">Specificity</span>
                                <span style="color: var(--primary-blue); font-weight: 700;">97.1%</span>
                            </div>
                            <div style="display: flex; justify-content: space-between; align-items: center; padding: 1rem; background: var(--bg-light); border-radius: 8px;">
                                <span style="color: var(--text-dark); font-weight: 600;">Uptime</span>
                                <span style="color: var(--primary-blue); font-weight: 700;">99.9%</span>
                            </div>
                        </div>
                    </div>

                    <h3 style="color: var(--text-dark); margin: 2rem 0 1.5rem;">Security & Compliance</h3>
                    <div style="background: white; padding: 2rem; border-radius: 12px; box-shadow: var(--shadow-sm);">
                        <div style="display: grid; gap: 1rem;">
                            <div style="display: flex; align-items: center; gap: 0.75rem;">
                                <i class="fas fa-shield-alt" style="color: var(--accent-green); font-size: 1.25rem;"></i>
                                <span style="color: var(--text-dark); font-weight: 600;">HIPAA Compliant</span>
                            </div>
                            <div style="display: flex; align-items: center; gap: 0.75rem;">
                                <i class="fas fa-lock" style="color: var(--accent-green); font-size: 1.25rem;"></i>
                                <span style="color: var(--text-dark); font-weight: 600;">AES-256 Encryption</span>
                            </div>
                            <div style="display: flex; align-items: center; gap: 0.75rem;">
                                <i class="fas fa-certificate" style="color: var(--accent-green); font-size: 1.25rem;"></i>
                                <span style="color: var(--text-dark); font-weight: 600;">SOC 2 Type II Certified</span>
                            </div>
                            <div style="display: flex; align-items: center; gap: 0.75rem;">
                                <i class="fas fa-globe" style="color: var(--accent-green); font-size: 1.25rem;"></i>
                                <span style="color: var(--text-dark); font-weight: 600;">GDPR Compliant</span>
                            </div>
                            <div style="display: flex; align-items: center; gap: 0.75rem;">
                                <i class="fas fa-user-shield" style="color: var(--accent-green); font-size: 1.25rem;"></i>
                                <span style="color: var(--text-dark); font-weight: 600;">ISO 27001 Certified</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Call to Action Section -->
    <section class="section" style="background: linear-gradient(135deg, var(--primary-blue), var(--secondary-blue)); color: white; text-align: center;">
        <div class="container">
            <h2 style="font-size: 2.5rem; margin-bottom: 1rem;">Ready to Get Started?</h2>
            <p style="font-size: 1.25rem; margin-bottom: 2rem; opacity: 0.9;">Take the first step towards early dementia detection with our AI-powered screening tool.</p>

            <div style="display: flex; gap: 1rem; justify-content: center; flex-wrap: wrap;">
                <button type="button" onclick="startScreening(); return false;" class="cta-button" style="background: var(--accent-teal);">
                    <i class="fas fa-microphone"></i>
                    Start Screening
                </button>
                <a href="audio_upload/history/" class="cta-button" style="background: rgba(255,255,255,0.2); border: 2px solid white;">
                    <i class="fas fa-history"></i>
                    View History
                </a>
            </div>

            <div style="margin-top: 3rem; padding-top: 2rem; border-top: 1px solid rgba(255,255,255,0.2);">
                <p style="font-size: 0.9rem; opacity: 0.8; margin-bottom: 1rem;">
                    <strong>Disclaimer:</strong> This tool is for screening purposes only and should not replace professional medical diagnosis.
                    Always consult with qualified healthcare professionals for proper evaluation and treatment.
                </p>

                <div style="display: flex; justify-content: center; gap: 2rem; flex-wrap: wrap; font-size: 0.9rem; opacity: 0.8;">
                    <span><i class="fas fa-shield-alt"></i> HIPAA Compliant</span>
                    <span><i class="fas fa-lock"></i> Secure & Private</span>
                    <span><i class="fas fa-certificate"></i> Clinically Validated</span>
                    <span><i class="fas fa-clock"></i> Instant Results</span>
                </div>
            </div>
        </div>
    </section>

    <!-- Configuration Script -->
    <script>
        // Use Django template variables to pass configuration
        window.API_BASE_URL = "{{ API_BASE_URL }}";
        window.LOCAL_BASE_URL = "{{ LOCAL_BASE_URL }}";

        // Fallback configuration (if template variables are empty)
        if (!window.API_BASE_URL || window.API_BASE_URL === '') {
            window.API_BASE_URL = "http://**************:8001";
        }
        if (!window.LOCAL_BASE_URL || window.LOCAL_BASE_URL === '') {
            window.LOCAL_BASE_URL = "http://**************:8000";
        }

        console.log('🔧 Home page configuration:', {
            API_BASE_URL: window.API_BASE_URL,
            LOCAL_BASE_URL: window.LOCAL_BASE_URL
        });
    </script>

    <!-- Authentication and Navigation Script -->
    <script>
        /**
         * Check if user is authenticated
         */
        function isUserAuthenticated() {
            const token = localStorage.getItem('access_token');
            console.log('🔍 Checking authentication - token exists:', !!token);

            if (!token) {
                console.log('❌ No token found');
                return false;
            }

            try {
                // Simple JWT decode (without verification)
                const payload = JSON.parse(atob(token.split('.')[1]));
                const now = Date.now() / 1000;

                console.log('🔍 Token payload:', {
                    exp: payload.exp,
                    now: now,
                    expired: payload.exp < now,
                    user_id: payload.user_id,
                    email: payload.email
                });

                // Check if token is expired
                if (payload.exp < now) {
                    console.log('❌ Token expired, removing from localStorage');
                    localStorage.removeItem('access_token');
                    return false;
                }

                console.log('✅ Token is valid');
                return true;
            } catch (error) {
                console.error('❌ Error checking authentication:', error);
                localStorage.removeItem('access_token');
                return false;
            }
        }

        /**
         * Handle start screening button click
         */
        function startScreening() {
            console.log('🔄 Start screening button clicked');

            // 添加更详细的调试信息
            const token = localStorage.getItem('access_token');
            console.log('🔍 Token check:', {
                hasToken: !!token,
                tokenLength: token ? token.length : 0,
                tokenPreview: token ? token.substring(0, 20) + '...' : 'null'
            });

            const isAuth = isUserAuthenticated();
            console.log('🔍 Authentication result:', isAuth);

            if (isAuth) {
                console.log('✅ User is authenticated, redirecting to audio upload');
                window.location.href = '/audio_upload/';
            } else {
                console.log('⚠️ User not authenticated, redirecting to login');
                // Store the intended destination
                sessionStorage.setItem('redirectAfterLogin', '/audio_upload/');
                console.log('💾 Stored redirect URL in sessionStorage');
                window.location.href = '/login/';
            }
        }

        /**
         * Check for redirect after login
         */
        function checkRedirectAfterLogin() {
            const redirectUrl = sessionStorage.getItem('redirectAfterLogin');
            if (redirectUrl && isUserAuthenticated()) {
                console.log('🔄 Redirecting after login to:', redirectUrl);
                sessionStorage.removeItem('redirectAfterLogin');
                window.location.href = redirectUrl;
            }
        }

        // Check for redirect when page loads
        document.addEventListener('DOMContentLoaded', () => {
            checkRedirectAfterLogin();

            // 确保函数在全局作用域中可用
            window.startScreening = startScreening;
            window.isUserAuthenticated = isUserAuthenticated;

            console.log('✅ Authentication functions loaded');

            // 测试函数是否可用
            if (typeof window.startScreening === 'function') {
                console.log('✅ startScreening function is available globally');
            } else {
                console.error('❌ startScreening function is not available');
            }
        });
    </script>

    <!-- Modern Language Selector -->
    <script src="{% static 'js/components/language-selector.js' %}"></script>
    <script>
        // Initialize language selector with modern configuration
        document.addEventListener('DOMContentLoaded', () => {
            // Create custom styles for home page to override default positioning
            const homeStyles = document.createElement('style');
            homeStyles.textContent = `
                .lang-selector.top-right {
                    top: 20px !important;
                }
                .lang-notification {
                    top: 80px !important;
                }
                @media (max-width: 768px) {
                    .lang-selector {
                        top: 15px !important;
                    }
                    .lang-notification {
                        top: 65px !important;
                    }
                }
            `;
            document.head.appendChild(homeStyles);

            const languageSelector = new LanguageSelector({
                position: 'top-right',
                theme: 'light',
                supportedLanguages: ['en'],
                defaultLanguage: 'en',
                showNotification: true,
                onLanguageChange: (newLang, oldLang) => {
                    console.log(`Language changed from ${oldLang} to ${newLang}`);

                    // Here you could implement actual language switching logic
                    // For now, we only support English
                    if (newLang !== 'en') {
                        console.warn('Only English is currently supported');
                    }
                }
            });

            // Listen for language change events
            document.addEventListener('languagechange', (event) => {
                const { newLanguage, languageData } = event.detail;

                // Update page title or other elements based on language
                document.documentElement.lang = newLanguage;

                // Analytics tracking (if needed)
                if (typeof gtag !== 'undefined') {
                    gtag('event', 'language_change', {
                        'language': newLanguage,
                        'previous_language': event.detail.oldLanguage
                    });
                }
            });

            // Show language support info on first visit
            if (!localStorage.getItem('language-info-shown')) {
                setTimeout(() => {
                    const info = document.createElement('div');
                    info.style.cssText = `
                        position: fixed;
                        bottom: 20px;
                        left: 20px;
                        background: rgba(0, 0, 0, 0.8);
                        color: white;
                        padding: 12px 16px;
                        border-radius: 8px;
                        font-size: 14px;
                        z-index: 999;
                        display: flex;
                        align-items: center;
                        gap: 8px;
                        max-width: 300px;
                        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
                    `;

                    info.innerHTML = `
                        <i class="fas fa-info-circle" style="color: #3b82f6;"></i>
                        <span>Currently supporting English only. More languages coming soon!</span>
                    `;

                    document.body.appendChild(info);

                    // Auto-hide after 5 seconds
                    setTimeout(() => {
                        info.style.opacity = '0';
                        info.style.transition = 'opacity 0.3s ease';
                        setTimeout(() => info.remove(), 300);
                    }, 5000);

                    localStorage.setItem('language-info-shown', 'true');
                }, 2000);
            }
        });
    </script>

</body>
</html>

{% endblock %}