<!DOCTYPE html>
<html lang="en" class="h-full">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>HiSage - AI-Powered Dementia Screening Platform</title>

    <!-- Professional Medical Typography -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Source+Sans+Pro:wght@300;400;600;700;900&family=Merriweather:wght@300;400;700;900&family=IBM+Plex+Mono:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css" integrity="sha512-DTOQO9RWCH3ppGqcWaEA1BIZOC6xxalwEsw9c2QQeAIftl+Vegovlnee1c9QX4TctnWMn13TZye+giMm8e2LwA==" crossorigin="anonymous" referrerpolicy="no-referrer">

    <!-- Custom Date Picker Styles -->

    <!-- Optimized Application Styles -->
    {% load static %}
    <link rel="stylesheet" href="{% static 'css/upload.css' %}">

    <!-- Button styling to match link appearance -->
    <style>
        button.nav-btn {
            border: none;
            cursor: pointer;
            font-family: inherit;
            font-size: inherit;
            background: inherit;
            color: inherit;
            text-decoration: none;
        }

        button.nav-btn:hover {
            text-decoration: none;
        }
    </style>

    <!-- Application Configuration -->
    <script>
        window.API_BASE_URL = "{{ API_BASE_URL }}";
        window.CSRF_TOKEN = "{{ csrf_token }}";

        // Force English locale for the entire page
        document.documentElement.lang = 'en-US';
        document.documentElement.dir = 'ltr';

        // Set navigator language override for date picker
        if (typeof navigator !== 'undefined') {
            Object.defineProperty(navigator, 'language', {
                get: function() { return 'en-US'; }
            });
            Object.defineProperty(navigator, 'languages', {
                get: function() { return ['en-US', 'en']; }
            });
        }
    </script>
</head>
<body>
    <main class="medical-app" role="main">
        <!-- Clinical Medical Header -->
        <header class="medical-header">
            <div class="header-layout">
                <nav class="nav-section nav-left">
                    <button onclick="goBack()" class="nav-btn" aria-label="Back">
                        <i class="fas fa-arrow-left" aria-hidden="true"></i>
                        <span>Back</span>
                    </button>
                </nav>

                <div class="brand-section">
                    <div class="brand-logo">
<!--?                        <div class="logo-symbol" aria-hidden="true">-->
<!--?                            <i class="fas fa-heartbeat"></i>-->
<!--?                        </div>-->
                    </div>
                    <h1 class="brand-title">HiSage</h1>
                    <p class="brand-subtitle">
                        AI-Powered Dementia Screening Platform
                    </p>
                </div>

                <nav class="nav-section nav-right">
                    <a href="{% url 'audio_upload:audio_history' %}" class="nav-btn" aria-label="View analysis history">
                        <i class="fas fa-history" aria-hidden="true"></i>
                        <span>History</span>
                    </a>
                </nav>
            </div>
        </header>

        <!-- Compact Medical Content -->
        <div class="medical-content">
            <!-- Combined Upload and Form Section -->
            <section class="combined-section">
                <div class="upload-header">
                    <h2>Audio Upload</h2>
                    <p>Upload patient audio for analysis</p>
                </div>

                <div class="upload-container-main">
                    <div class="upload-dropzone" id="upload-area" tabindex="0" role="button" aria-label="Click to select audio file or drag and drop">
                        <input
                            type="file"
                            id="file-input"
                            accept="audio/*,.wav,.mp3,.m4a,.aac,.ogg,.flac"
                            hidden
                            aria-describedby="file-requirements"
                        >
                        <div class="upload-icon-container" aria-hidden="true">
                            <i class="fas fa-microphone"></i>
                        </div>
                        <div class="upload-content" id="upload-text">
                            <span class="upload-title">Select Audio File</span>
                            <span class="upload-description">Click to browse or drag & drop</span>
                            <span class="upload-hint">WAV, MP3, M4A, AAC, OGG, FLAC (Max: 100MB)</span>
                        </div>
                    </div>

                    <div class="file-preview" id="file-info">
                        <div class="file-details">
                            <div class="file-icon">
                                <i class="fas fa-file-audio"></i>
                            </div>
                            <div class="file-meta">
                                <div class="file-name" id="file-name"></div>
                                <div class="file-size" id="file-size"></div>
                            </div>
                        </div>
                    </div>

                    <p id="file-requirements" class="sr-only">
                        Supported formats: WAV, MP3, M4A, AAC, OGG, FLAC. Maximum file size: 100MB.
                    </p>
                </div>

                <!-- Form Fields without header -->
                <div class="medical-form">
                    <form id="analysis-form" novalidate>
                        <div class="form-fields">
                            <!-- Speaker Relationship -->
                            <div class="form-group">
                                <label for="relationship" class="form-label">
                                    This audio is spoken by: <span class="optional">(Optional)</span>
                                </label>
                                <select id="relationship" class="form-control">
                                    <option value="">Select</option>
                                    <option value="my_self">Myself</option>
                                    <option value="my_father">My Father</option>
                                    <option value="my_mother">My Mother</option>
                                    <option value="my_father_in_law">My Father in Law</option>
                                    <option value="my_mother_in_law">My Mother in Law</option>
                                    <option value="my_grandfather">My Grandfather</option>
                                    <option value="my_grandmother">My Grandmother</option>
                                    <option value="my_friend">My Friend</option>
                                    <option value="others">Other</option>
                                </select>

                                <!-- Custom Relationship - positioned right after relationship select -->
                                <div id="other-relationship-group" class="form-subgroup hidden">
                                    <label for="other-relationship" class="form-label">
                                        Specify<span class="required">*</span>
                                    </label>
                                    <input
                                        type="text"
                                        id="other-relationship"
                                        class="form-control"
                                        placeholder=""
                                        maxlength="50"
                                    >
                                </div>
                            </div>

                            <!-- Occupation -->
                            <div class="form-group">
                                <label for="occupation" class="form-label">
                                    Occupation <span class="optional">(Optional)</span>
                                </label>
                                <select id="occupation" class="form-control">
                                    <option value="">Select occupation</option>

                                    <!-- Healthcare & Medical -->
                                    <optgroup label="Healthcare & Medical">
                                        <option value="physician">Physician/Doctor</option>
                                        <option value="nurse">Registered Nurse</option>
                                        <option value="pharmacist">Pharmacist</option>
                                        <option value="dentist">Dentist</option>
                                        <option value="therapist">Physical/Occupational Therapist</option>
                                        <option value="medical_technician">Medical Technician</option>
                                        <option value="veterinarian">Veterinarian</option>
                                        <option value="medical_assistant">Medical Assistant</option>
                                        <option value="radiologic_technologist">Radiologic Technologist</option>
                                        <option value="respiratory_therapist">Respiratory Therapist</option>
                                    </optgroup>

                                    <!-- Education -->
                                    <optgroup label="Education">
                                        <option value="elementary_teacher">Elementary School Teacher</option>
                                        <option value="secondary_teacher">Secondary School Teacher</option>
                                        <option value="professor">College Professor</option>
                                        <option value="school_administrator">School Administrator</option>
                                        <option value="special_education_teacher">Special Education Teacher</option>
                                        <option value="librarian">Librarian</option>
                                        <option value="counselor">School Counselor</option>
                                    </optgroup>

                                    <!-- Technology & Engineering -->
                                    <optgroup label="Technology & Engineering">
                                        <option value="software_engineer">Software Engineer</option>
                                        <option value="data_scientist">Data Scientist</option>
                                        <option value="web_developer">Web Developer</option>
                                        <option value="it_specialist">IT Specialist</option>
                                        <option value="civil_engineer">Civil Engineer</option>
                                        <option value="mechanical_engineer">Mechanical Engineer</option>
                                        <option value="electrical_engineer">Electrical Engineer</option>
                                        <option value="computer_systems_analyst">Computer Systems Analyst</option>
                                        <option value="network_administrator">Network Administrator</option>
                                        <option value="cybersecurity_specialist">Cybersecurity Specialist</option>
                                    </optgroup>

                                    <!-- Business & Finance -->
                                    <optgroup label="Business & Finance">
                                        <option value="manager">Manager</option>
                                        <option value="accountant">Accountant</option>
                                        <option value="financial_advisor">Financial Advisor</option>
                                        <option value="sales_representative">Sales Representative</option>
                                        <option value="marketing_specialist">Marketing Specialist</option>
                                        <option value="human_resources">Human Resources Specialist</option>
                                        <option value="business_analyst">Business Analyst</option>
                                        <option value="project_manager">Project Manager</option>
                                        <option value="real_estate_agent">Real Estate Agent</option>
                                        <option value="insurance_agent">Insurance Agent</option>
                                    </optgroup>

                                    <!-- Legal & Government -->
                                    <optgroup label="Legal & Government">
                                        <option value="lawyer">Lawyer</option>
                                        <option value="paralegal">Paralegal</option>
                                        <option value="judge">Judge</option>
                                        <option value="police_officer">Police Officer</option>
                                        <option value="firefighter">Firefighter</option>
                                        <option value="government_employee">Government Employee</option>
                                        <option value="social_worker">Social Worker</option>
                                        <option value="probation_officer">Probation Officer</option>
                                    </optgroup>

                                    <!-- Construction & Trades -->
                                    <optgroup label="Construction & Trades">
                                        <option value="architect">Architect</option>
                                        <option value="construction_worker">Construction Worker</option>
                                        <option value="electrician">Electrician</option>
                                        <option value="plumber">Plumber</option>
                                        <option value="carpenter">Carpenter</option>
                                        <option value="hvac_technician">HVAC Technician</option>
                                        <option value="welder">Welder</option>
                                        <option value="roofer">Roofer</option>
                                    </optgroup>

                                    <!-- Service Industry -->
                                    <optgroup label="Service Industry">
                                        <option value="retail_worker">Retail Sales Associate</option>
                                        <option value="restaurant_worker">Restaurant Worker</option>
                                        <option value="customer_service">Customer Service Representative</option>
                                        <option value="security_guard">Security Guard</option>
                                        <option value="janitor">Janitor/Cleaner</option>
                                        <option value="hairdresser">Hairdresser/Cosmetologist</option>
                                        <option value="personal_trainer">Personal Trainer</option>
                                        <option value="childcare_worker">Childcare Worker</option>
                                    </optgroup>

                                    <!-- Transportation & Logistics -->
                                    <optgroup label="Transportation & Logistics">
                                        <option value="truck_driver">Truck Driver</option>
                                        <option value="delivery_driver">Delivery Driver</option>
                                        <option value="pilot">Pilot</option>
                                        <option value="flight_attendant">Flight Attendant</option>
                                        <option value="bus_driver">Bus Driver</option>
                                        <option value="taxi_driver">Taxi/Uber Driver</option>
                                        <option value="logistics_coordinator">Logistics Coordinator</option>
                                        <option value="warehouse_worker">Warehouse Worker</option>
                                    </optgroup>

                                    <!-- Arts, Media & Entertainment -->
                                    <optgroup label="Arts, Media & Entertainment">
                                        <option value="artist">Artist</option>
                                        <option value="writer">Writer</option>
                                        <option value="journalist">Journalist</option>
                                        <option value="photographer">Photographer</option>
                                        <option value="graphic_designer">Graphic Designer</option>
                                        <option value="musician">Musician</option>
                                        <option value="actor">Actor</option>
                                        <option value="video_editor">Video Editor</option>
                                    </optgroup>

                                    <!-- Agriculture & Food -->
                                    <optgroup label="Agriculture & Food">
                                        <option value="farmer">Farmer</option>
                                        <option value="chef">Chef</option>
                                        <option value="food_service_worker">Food Service Worker</option>
                                        <option value="agricultural_worker">Agricultural Worker</option>
                                        <option value="food_inspector">Food Inspector</option>
                                    </optgroup>

                                    <!-- Other Categories -->
                                    <optgroup label="Other">
                                        <option value="student">Student</option>
                                        <option value="retired">Retired</option>
                                        <option value="unemployed">Unemployed</option>
                                        <option value="homemaker">Homemaker</option>
                                        <option value="volunteer">Volunteer</option>
                                        <option value="self_employed">Self-Employed</option>
                                        <option value="others">Other</option>
                                    </optgroup>
                                </select>

                                <!-- Custom Occupation - positioned right after occupation select -->
                                <div id="other-occupation-group" class="form-subgroup hidden">
                                    <label for="other-occupation" class="form-label">
                                        Specify Occupation <span class="required">*</span>
                                    </label>
                                    <input
                                        type="text"
                                        id="other-occupation"
                                        class="form-control"
                                        placeholder="Enter occupation"
                                        maxlength="50"
                                    >
                                </div>
                            </div>

                            <!-- Date of Birth -->
                            <div class="form-group full-width">
                                <label for="date-of-birth" class="form-label">
                                    Date of Birth <span class="optional">(Optional)</span>
                                </label>
                                <div class="date-selector-container">
                                    <div class="date-dropdowns">
                                        <select id="birth-day" name="birthDay" class="form-control date-select">
                                            <option value="">Day</option>
                                        </select>
                                        <select id="birth-month" name="birthMonth" class="form-control date-select">
                                            <option value="">Month</option>
                                            <option value="1">January</option>
                                            <option value="2">February</option>
                                            <option value="3">March</option>
                                            <option value="4">April</option>
                                            <option value="5">May</option>
                                            <option value="6">June</option>
                                            <option value="7">July</option>
                                            <option value="8">August</option>
                                            <option value="9">September</option>
                                            <option value="10">October</option>
                                            <option value="11">November</option>
                                            <option value="12">December</option>
                                        </select>
                                        <select id="birth-year" name="birthYear" class="form-control date-select">
                                            <option value="">Year</option>
                                        </select>
                                    </div>
                                    <input type="hidden" id="date-of-birth" name="dateOfBirth">
                                </div>
                                <div class="date-format-hint">Select your date of birth using the dropdowns above</div>
                            </div>


                        </div>

                        <!-- Submit Button in Form -->
                        <div class="form-button-section">
                            <button
                                type="submit"
                                id="submit-btn"
                                class="btn btn-primary btn-full"
                                disabled
                            >
                                <span class="spinner" aria-hidden="true"></span>
                                <span class="btn-text">
                                    <i class="fas fa-upload" aria-hidden="true"></i>
                                    Upload & Analyze
                                </span>
                            </button>

                            <!-- Progress Container -->
                            <div class="progress-container" id="progress-container">
                                <div class="progress-bar">
                                    <div class="progress-bar-fill" id="progress-fill"></div>
                                </div>
                                <div class="progress-text" id="progress-text">Processing...</div>
                            </div>

                            <!-- Message Container -->
                            <div class="message-container" id="message-container" role="status" aria-live="polite">
                                <!-- Clinical messages will be displayed here -->
                            </div>
                        </div>
                    </form>
                </div>
            </section>
        </div>
    </main>
    <!-- User Guidelines and Privacy Agreement Modal -->
    <div class="terms-modal-overlay" id="terms-modal-overlay">
        <div class="terms-modal">
            <div class="terms-modal-header">
                <h3><i class="fas fa-info-circle"></i> User Guidelines and Privacy Agreement</h3>
            </div>

            <div class="terms-modal-content">
                <div class="terms-section">
                    <h4><i class="fas fa-microphone"></i> Cookie Theft Picture Recording Guidelines</h4>
                    <div class="terms-content">
                        <ul>
                            <li>Look at the <a href="#" class="cookie-theft-link" onclick="showCookieTheftPicture(event)">Cookie Theft picture</a> carefully and describe everything you see happening in English</li>
                            <li>Record in a quiet environment to minimize background noise and ensure clear audio quality</li>
                            <li>Keep your recording between 1-3 minutes in length for best results</li>
                            <li>Use a good quality microphone or recording device when possible</li>
                            <li>Ensure your audio file is in one of these supported formats: WAV, MP3, M4A, AAC, OGG, or FLAC</li>
                            <li>File size must not exceed 100MB to ensure successful upload and processing</li>
                        </ul>
                    </div>
                </div>

                <div class="terms-section">
                    <h4><i class="fas fa-upload"></i> Audio Upload Instructions</h4>
                    <div class="terms-content">
                        <ul>
                            <li>Currently, we only support speech analysis in English</li>
                            <li>Click the "Select Audio File" button to browse and choose your audio recording</li>
                            <li>Alternatively, you can drag and drop your audio file directly into the upload area</li>
                            <li>Click the "Start Analysis" button to submit your audio for cognitive health processing</li>
                            <li>Wait for the upload and initial processing to complete - this may take several minutes</li>
                            <li>Once analysis is complete, you can view detailed results in the History section</li>
                            <li>You can upload multiple recordings over time to track changes in cognitive patterns</li>
                        </ul>
                    </div>
                </div>

                <div class="terms-section">
                    <h4><i class="fas fa-shield-alt"></i> Privacy Policy and Data Protection</h4>
                    <div class="terms-content">
                        <ul>
                            <li>Your audio recordings and personal data will be used exclusively for cognitive health analysis purposes</li>
                            <li>We are committed to protecting your personal privacy and maintaining the highest standards of data security</li>
                            <li>All audio files are encrypted during transmission and storage using industry-standard security protocols</li>
                            <li>Your data will never be shared with third parties</li>
                            <li>You retain full ownership of your audio recordings and can request deletion at any time</li>
                            <li>Analysis results are for informational purposes only and should not replace professional medical diagnosis</li>
                            <li>Our system complies with HIPAA, GDPR, and other relevant data protection regulations</li>
                            <li>We may use anonymized, aggregated data for research purposes to improve our analysis algorithms</li>
                            <li>By using this service, you acknowledge understanding of our data processing practices</li>
                        </ul>
                    </div>
                </div>
            </div>

            <div class="terms-modal-footer">
                <div class="countdown-info" id="countdown-info">
                    <i class="fas fa-clock"></i>
                    <span>Please read the above content carefully. You can proceed in <span id="countdown-timer">5</span> seconds</span>
                </div>
                <div class="terms-modal-buttons">
                    <button type="button" class="terms-btn terms-btn-decline" id="terms-decline-btn">
                        <i class="fas fa-times"></i> Decline
                    </button>
                    <button type="button" class="terms-btn terms-btn-accept" id="terms-accept-btn" disabled>
                        <i class="fas fa-check"></i> I Understand and Agree
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Cookie Theft Picture Modal -->
    <div class="cookie-theft-modal-overlay" id="cookie-theft-modal-overlay">
        <div class="cookie-theft-modal">
            <div class="cookie-theft-modal-header">
                <h3><i class="fas fa-image"></i> Cookie Theft Picture</h3>
                <button type="button" class="cookie-theft-close-btn" onclick="hideCookieTheftPicture()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="cookie-theft-modal-content">
                <img src="{% static 'images/Cookie-Theft-Picture.png' %}" alt="Cookie Theft Picture" class="cookie-theft-image">
                <p class="cookie-theft-description">
                    This is the Cookie Theft picture used for the speech analysis task. Please describe everything you see in this image during your recording.
                </p>
            </div>
        </div>
    </div>

    <!-- Back Button Functionality -->
    <script>
        // Back button functionality
        function goBack() {
            // Check if there's a previous page in history
            if (window.history.length > 1) {
                window.history.back();
            } else {
                // If no history, go to home page as fallback
                window.location.href = '/';
            }
        }

        // Cookie Theft Picture Modal Functions
        function showCookieTheftPicture(event) {
            event.preventDefault();
            console.log('🖼️ Showing Cookie Theft picture modal');

            const modal = document.getElementById('cookie-theft-modal-overlay');
            if (modal) {
                modal.classList.add('show');
                document.body.style.overflow = 'hidden';
                console.log('🖼️ Cookie Theft picture modal displayed');
            } else {
                console.error('❌ Cookie Theft modal not found');
            }
        }

        function hideCookieTheftPicture() {
            console.log('🖼️ Hiding Cookie Theft picture modal');

            const modal = document.getElementById('cookie-theft-modal-overlay');
            if (modal) {
                modal.classList.remove('show');
                document.body.style.overflow = '';
                console.log('🖼️ Cookie Theft picture modal hidden');
            }
        }

        // Close modal when clicking outside the content
        document.addEventListener('DOMContentLoaded', function() {
            const modalOverlay = document.getElementById('cookie-theft-modal-overlay');
            if (modalOverlay) {
                modalOverlay.addEventListener('click', function(e) {
                    if (e.target === modalOverlay) {
                        hideCookieTheftPicture();
                    }
                });
            }
        });
    </script>

    <!-- External Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/jwt-decode@3.1.2/build/jwt-decode.min.js"></script>





    <!-- Optimized Application Scripts -->
    <script src="{% static 'js/upload.js' %}"></script>

</body>
</html>