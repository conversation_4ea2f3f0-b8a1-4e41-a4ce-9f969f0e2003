/**
 * NeuroVoice Analytics Upload Application
 * Optimized ES6+ implementation with performance improvements
 */
class AudioUploadApp {
    constructor() {
        // DOM Elements
        this.elements = {
            fileInput: document.getElementById('file-input'),
            uploadArea: document.getElementById('upload-area'),
            uploadText: document.getElementById('upload-text'),
            fileInfo: document.getElementById('file-info'),
            fileName: document.getElementById('file-name'),
            fileSize: document.getElementById('file-size'),
            form: document.getElementById('analysis-form'),
            submitBtn: document.getElementById('submit-btn'),
            progressContainer: document.getElementById('progress-container'),
            progressFill: document.getElementById('progress-fill'),
            progressText: document.getElementById('progress-text'),
            messageContainer: document.getElementById('message-container'),
            relationshipSelect: document.getElementById('relationship'),
            otherRelationshipGroup: document.getElementById('other-relationship-group'),
            otherRelationshipInput: document.getElementById('other-relationship'),
            occupationSelect: document.getElementById('occupation'),
            otherOccupationGroup: document.getElementById('other-occupation-group'),
            otherOccupationInput: document.getElementById('other-occupation'),
            dateOfBirth: document.getElementById('date-of-birth'),
            // 用户协议弹窗相关元素
            termsModalOverlay: document.getElementById('terms-modal-overlay'),
            termsAcceptBtn: document.getElementById('terms-accept-btn'),
            termsDeclineBtn: document.getElementById('terms-decline-btn'),
            countdownTimer: document.getElementById('countdown-timer')
        };

        // Configuration
        this.config = {
            maxFileSize: 100 * 1024 * 1024, // 100MB
            allowedTypes: ['audio/wav', 'audio/mpeg', 'audio/mp4', 'audio/aac', 'audio/ogg', 'audio/flac'],
            allowedExtensions: ['.wav', '.mp3', '.m4a', '.aac', '.ogg', '.flac'],
            apiBaseUrl: window.API_BASE_URL
        };

        // State
        this.state = {
            selectedFile: null,
            isUploading: false,
            uploadProgress: 0,
            hasAgreedToTerms: false,
            isFirstTimeClicking: true,
            countdownActive: false,
            pendingFile: null // File waiting for terms agreement
        };

        this.init();
    }

    /**
     * Initialize the application
     */
    init() {
        // Check authentication first
        if (!this.getValidToken()) {
            console.warn('⚠️ User not authenticated, redirecting to login');
            this.redirectToLogin();
            return;
        }

        this.bindEvents();
        this.updateSubmitButton();
        this.setupAccessibility();
        this.setupDatePicker();
        this.preloadTermsModal();

        // Modal elements initialized successfully

        // Debug code removed - no longer forcing hasAgreedToTerms to false
    }

    /**
     * Bind all event listeners with single-fire protection
     */
    bindEvents() {
        // File upload events - 修改为检查协议状态
        this.elements.uploadArea.addEventListener('click', () => this.handleUploadAreaClick());
        this.elements.uploadArea.addEventListener('dragover', this.handleDragOver.bind(this));
        this.elements.uploadArea.addEventListener('dragleave', this.handleDragLeave.bind(this));
        this.elements.uploadArea.addEventListener('drop', this.handleDrop.bind(this));
        this.elements.uploadArea.addEventListener('keydown', this.handleKeyDown.bind(this));
        this.elements.fileInput.addEventListener('change', this.handleFileSelect.bind(this));

        // Form events with single binding
        this.elements.form.addEventListener('submit', this.handleSubmit.bind(this));

        // Use 'change' event with once-only protection
        this.elements.relationshipSelect.addEventListener('change', this.createSingleFireHandler(this.handleRelationshipChange.bind(this)));
        this.elements.occupationSelect.addEventListener('change', this.createSingleFireHandler(this.handleOccupationChange.bind(this)));

        // Validation events
        this.elements.otherRelationshipInput.addEventListener('input', this.debounce(this.validateForm.bind(this), 300));
        this.elements.otherOccupationInput.addEventListener('input', this.debounce(this.validateForm.bind(this), 300));

        // 用户协议弹窗事件
        this.elements.termsAcceptBtn.addEventListener('click', this.handleTermsAccept.bind(this));
        this.elements.termsDeclineBtn.addEventListener('click', this.handleTermsDecline.bind(this));

        // 点击弹窗外部关闭（仅在拒绝后允许）
        this.elements.termsModalOverlay.addEventListener('click', (e) => {
            if (e.target === this.elements.termsModalOverlay && !this.state.hasAgreedToTerms) {
                this.hideTermsModal();
            }
        });
    }

    /**
     * Create a single-fire event handler to prevent double triggering
     */
    createSingleFireHandler(handler) {
        let isProcessing = false;
        return function(event) {
            if (isProcessing) {
                return;
            }
            isProcessing = true;

            // Execute the handler
            handler(event);

            // Reset the flag after a short delay
            setTimeout(() => {
                isProcessing = false;
            }, 50);
        };
    }

    /**
     * Debounce function to prevent excessive calls
     */
    debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    }

    /**
     * Setup accessibility features
     */
    setupAccessibility() {
        this.elements.uploadArea.setAttribute('aria-describedby', 'file-requirements');
        this.elements.fileInput.addEventListener('change', () => {
            if (this.state.selectedFile) {
                this.announceToScreenReader(`File selected: ${this.state.selectedFile.name}`);
            }
        });
    }

    /**
     * Setup simple dropdown date picker
     */
    setupDatePicker() {
        console.log('🔧 Setting up dropdown date picker...');

        // Get dropdown elements
        this.daySelect = document.getElementById('birth-day');
        this.monthSelect = document.getElementById('birth-month');
        this.yearSelect = document.getElementById('birth-year');
        this.hiddenDateInput = document.getElementById('date-of-birth');

        if (!this.daySelect || !this.monthSelect || !this.yearSelect || !this.hiddenDateInput) {
            console.error('❌ Date selector elements not found!');
            return;
        }

        // Initialize dropdowns
        this.initializeDateDropdowns();

        // Setup event listeners
        this.setupDateDropdownEvents();

        console.log('✅ Dropdown date picker setup complete');
    }

    /**
     * Initialize date dropdown options
     */
    initializeDateDropdowns() {
        // Populate day dropdown (1-31)
        for (let day = 1; day <= 31; day++) {
            const option = document.createElement('option');
            option.value = day;
            option.textContent = day;
            this.daySelect.appendChild(option);
        }

        // Populate year dropdown (current year - 200 years)
        const currentYear = new Date().getFullYear();
        const startYear = currentYear - 200;

        for (let year = currentYear; year >= startYear; year--) {
            const option = document.createElement('option');
            option.value = year;
            option.textContent = year;
            this.yearSelect.appendChild(option);
        }

        console.log(`✅ Date dropdowns initialized: ${startYear} - ${currentYear}`);
    }

    /**
     * Setup event listeners for date dropdowns
     */
    setupDateDropdownEvents() {
        // Add change listeners to all dropdowns
        this.daySelect.addEventListener('change', () => {
            this.updateDateFromDropdowns();
        });

        this.monthSelect.addEventListener('change', () => {
            this.updateDateFromDropdowns();
            this.updateDaysInMonth();
        });

        this.yearSelect.addEventListener('change', () => {
            this.updateDateFromDropdowns();
            this.updateDaysInMonth();
        });

        console.log('✅ Date dropdown events setup complete');
    }

    /**
     * Update days dropdown based on selected month and year
     */
    updateDaysInMonth() {
        const month = parseInt(this.monthSelect.value);
        const year = parseInt(this.yearSelect.value);

        if (!month || !year) return;

        // Get number of days in the selected month
        const daysInMonth = new Date(year, month, 0).getDate();
        const currentDay = parseInt(this.daySelect.value);

        // Clear existing day options (except the first "Day" option)
        while (this.daySelect.children.length > 1) {
            this.daySelect.removeChild(this.daySelect.lastChild);
        }

        // Add days for the selected month
        for (let day = 1; day <= daysInMonth; day++) {
            const option = document.createElement('option');
            option.value = day;
            option.textContent = day;
            this.daySelect.appendChild(option);
        }

        // Restore selected day if it's still valid
        if (currentDay && currentDay <= daysInMonth) {
            this.daySelect.value = currentDay;
        } else {
            this.daySelect.value = '';
        }

        console.log(`📅 Updated days for ${month}/${year}: ${daysInMonth} days`);
    }

    /**
     * Update hidden date input from dropdown selections
     */
    updateDateFromDropdowns() {
        const day = this.daySelect.value;
        const month = this.monthSelect.value;
        const year = this.yearSelect.value;

        if (day && month && year) {
            // Create date object to validate
            const date = new Date(year, month - 1, day);

            // Check if the date is valid
            if (date.getFullYear() == year &&
                date.getMonth() == month - 1 &&
                date.getDate() == day) {

                // Format as DD-MM-YYYY
                const formattedDay = ('0' + day).slice(-2);
                const formattedMonth = ('0' + month).slice(-2);
                const formattedDate = `${formattedDay}-${formattedMonth}-${year}`;

                // Update hidden input
                this.hiddenDateInput.value = formattedDate;
                this.selectedDate = date;

                // Validate the date
                if (this.validateDateOfBirth(date)) {
                    this.clearMessages();
                    this.validateForm();

                    // Update hint to show selected date
                    const hint = document.querySelector('.date-format-hint');
                    if (hint) {
                        hint.textContent = `Selected: ${formattedDate}`;
                        hint.style.color = 'var(--medical-primary)';
                        hint.style.fontWeight = '600';
                    }

                    console.log('✅ Date updated:', formattedDate);
                } else {
                    console.warn('⚠️ Invalid date selected');
                }
            } else {
                console.warn('⚠️ Invalid date combination');
                this.hiddenDateInput.value = '';
                this.selectedDate = null;
            }
        } else {
            // Clear if not all fields are selected
            this.hiddenDateInput.value = '';
            this.selectedDate = null;

            // Reset hint text
            const hint = document.querySelector('.date-format-hint');
            if (hint) {
                hint.textContent = 'Select your date of birth using the dropdowns above';
                hint.style.color = 'var(--text-muted)';
                hint.style.fontWeight = 'normal';
            }
        }
    }







    /**
     * Format date for display (DD-MM-YYYY)
     */
    formatDate(date) {
        if (!date) return '';

        const day = ('0' + date.getDate()).slice(-2);
        const month = ('0' + (date.getMonth() + 1)).slice(-2);
        const year = date.getFullYear();

        return `${day}-${month}-${year}`;
    }

    /**
     * Parse DD-MM-YYYY format to Date object
     */
    parseDate(dateString) {
        if (!dateString) return null;

        const parts = dateString.split('-');
        if (parts.length !== 3) return null;

        const day = parseInt(parts[0], 10);
        const month = parseInt(parts[1], 10) - 1; // Month is 0-indexed
        const year = parseInt(parts[2], 10);

        return new Date(year, month, day);
    }

    /**
     * Validate date format DD-MM-YYYY
     */
    isValidDateFormat(dateString) {
        const regex = /^(0[1-9]|[12][0-9]|3[01])-(0[1-9]|1[0-2])-\d{4}$/;
        if (!regex.test(dateString)) return false;

        const parts = dateString.split('-');
        const day = parseInt(parts[0], 10);
        const month = parseInt(parts[1], 10);
        const year = parseInt(parts[2], 10);

        const date = new Date(year, month - 1, day);
        return date.getFullYear() === year &&
               date.getMonth() === month - 1 &&
               date.getDate() === day;
    }



    /**
     * Validate selected date of birth
     */
    validateDateOfBirth(date) {
        if (!date) return false;

        const today = new Date();
        today.setHours(0, 0, 0, 0);

        const selectedDate = new Date(date);
        selectedDate.setHours(0, 0, 0, 0);

        // Check if date is valid
        if (isNaN(selectedDate.getTime())) {
            this.showMessage('Please enter a valid date', 'error');
            return false;
        }

        // Check if date is in the future
        if (selectedDate > today) {
            this.showMessage('Date of birth cannot be in the future', 'error');
            return false;
        }

        // Check if date is too old
        const minDate = new Date('1900-01-01');
        if (selectedDate < minDate) {
            this.showMessage('Please enter a valid date of birth', 'error');
            return false;
        }

        // Check if person would be too young (optional validation)
        const age = this.calculateAge(selectedDate);
        if (age !== null && age < 0) {
            this.showMessage('Please enter a valid date of birth', 'error');
            return false;
        }

        return true;
    }

    /**
     * Handle drag over event
     */
    handleDragOver(event) {
        event.preventDefault();
        this.elements.uploadArea.classList.add('dragover');
    }

    /**
     * Handle drag leave event
     */
    handleDragLeave(event) {
        event.preventDefault();
        this.elements.uploadArea.classList.remove('dragover');
    }

    /**
     * Handle drop event
     */
    async handleDrop(event) {
        event.preventDefault();
        this.elements.uploadArea.classList.remove('dragover');

        const files = event.dataTransfer.files;
        if (files.length > 0) {
            try {
                // Call backend API to check current terms agreement status
                const hasAgreed = await this.checkTermsAgreementStatus();

                if (hasAgreed) {
                    this.selectFile(files[0]);
                } else {
                    console.log('📋 User has not agreed to terms, storing file and showing modal for drag & drop');
                    this.state.pendingFile = files[0]; // Store the file for later processing
                    this.showTermsModal();
                }
            } catch (error) {
                console.error('❌ Error checking terms agreement for drag & drop:', error);
                // On error, show terms modal as fallback
                this.state.pendingFile = files[0];
                this.showTermsModal();
            }
        }
    }

    /**
     * Handle keyboard navigation
     */
    handleKeyDown(event) {
        if (event.key === 'Enter' || event.key === ' ') {
            event.preventDefault();
            this.handleUploadAreaClick(); // Use the same logic as click
        }
    }

    /**
     * Handle file selection
     */
    async handleFileSelect(event) {
        const file = event.target.files[0];
        if (file) {
            try {
                // Double-check terms agreement via API (should already be agreed if we got here)
                const hasAgreed = await this.checkTermsAgreementStatus();

                if (hasAgreed) {
                    this.selectFile(file);
                } else {
                    console.log('📋 User has not agreed to terms, showing modal for file selection');
                    this.showTermsModal();
                    // Clear the file input
                    event.target.value = '';
                }
            } catch (error) {
                console.error('❌ Error checking terms agreement for file selection:', error);
                // On error, show terms modal as fallback
                this.showTermsModal();
                event.target.value = '';
            }
        }
    }

    /**
     * Select and validate file
     */
    selectFile(file) {
        // Validate file
        const validation = this.validateFile(file);
        if (!validation.isValid) {
            this.showMessage(validation.message, 'error');
            return;
        }

        // Update state
        this.state.selectedFile = file;
        this.elements.fileInput.files = this.createFileList(file);

        // Update UI
        this.updateFileDisplay();
        this.updateSubmitButton();
        this.clearMessages();
    }

    /**
     * Validate selected file
     */
    validateFile(file) {
        // Check file size
        if (file.size > this.config.maxFileSize) {
            return {
                isValid: false,
                message: `File size too large. Maximum size is ${this.formatFileSize(this.config.maxFileSize)}.`
            };
        }

        // Check file type
        const isValidType = this.config.allowedTypes.includes(file.type) ||
            this.config.allowedExtensions.some(ext => file.name.toLowerCase().endsWith(ext));

        if (!isValidType) {
            return {
                isValid: false,
                message: 'Invalid file type. Please select an audio file (WAV, MP3, M4A, AAC, OGG, FLAC).'
            };
        }

        return { isValid: true };
    }

    /**
     * Update file display
     */
    updateFileDisplay() {
        if (this.state.selectedFile) {
            this.elements.uploadText.classList.add('has-file');
            this.elements.uploadText.querySelector('.upload-title').textContent = 'File Selected';
            this.elements.uploadText.querySelector('.upload-description').textContent = 'Click to change file';

            this.elements.fileName.textContent = this.state.selectedFile.name;
            this.elements.fileSize.textContent = this.formatFileSize(this.state.selectedFile.size);
            this.elements.fileInfo.classList.add('show');
        } else {
            this.elements.uploadText.classList.remove('has-file');
            this.elements.uploadText.querySelector('.upload-title').textContent = 'Select Audio File';
            this.elements.uploadText.querySelector('.upload-description').textContent = 'Click to browse or drag & drop';
            this.elements.fileInfo.classList.remove('show');
        }
    }

    /**
     * Handle relationship selection change - single fire only
     */
    handleRelationshipChange(event) {
        const selectedValue = event.target.value;
        const isOther = selectedValue === 'others';

        // Toggle the other relationship group
        this.toggleGroup(this.elements.otherRelationshipGroup, isOther);

        // Focus on the input if showing
        if (isOther && this.elements.otherRelationshipInput) {
            setTimeout(() => {
                this.elements.otherRelationshipInput.focus();
            }, 100);
        }

        this.validateForm();
    }

    /**
     * Handle occupation selection change - single fire only
     */
    handleOccupationChange(event) {
        const selectedValue = event.target.value;
        const isOther = selectedValue === 'others';

        // Toggle the other occupation group
        this.toggleGroup(this.elements.otherOccupationGroup, isOther);

        // Focus on the input if showing
        if (isOther && this.elements.otherOccupationInput) {
            setTimeout(() => {
                this.elements.otherOccupationInput.focus();
            }, 100);
        }

        this.validateForm();
    }

    /**
     * Toggle form group visibility with animation
     */
    toggleGroup(group, show) {
        if (!group) return;

        const input = group.querySelector('input');
        if (!input) return;

        if (show) {
            // Show the group
            group.classList.remove('hidden');
            group.style.display = 'block';
            input.setAttribute('required', '');

            // Animate in
            requestAnimationFrame(() => {
                group.style.opacity = '1';
                group.style.transform = 'translateY(0)';
            });
        } else {
            // Hide the group
            group.style.opacity = '0';
            group.style.transform = 'translateY(-10px)';

            // Clean up after animation
            setTimeout(() => {
                group.classList.add('hidden');
                group.style.display = 'none';
                input.removeAttribute('required');
                input.value = '';
            }, 200);
        }
    }

    /**
     * Calculate age from birth date
     */
    calculateAge(birthDate) {
        if (!birthDate) return null;

        const today = new Date();
        const birth = new Date(birthDate);

        // Ensure we're working with valid dates
        if (isNaN(birth.getTime()) || isNaN(today.getTime())) {
            return null;
        }

        let age = today.getFullYear() - birth.getFullYear();
        const monthDiff = today.getMonth() - birth.getMonth();

        if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birth.getDate())) {
            age--;
        }

        return age >= 0 ? age : null;
    }

    /**
     * Setup fallback native date picker if Vanilla Calendar fails
     */
    setupFallbackDatePicker() {
        console.log('Setting up fallback native date picker');

        // Hide the calendar button
        const calendarBtn = document.querySelector('.calendar-picker-btn');
        if (calendarBtn) {
            calendarBtn.style.display = 'none';
        }

        // Change input type to date for native picker
        this.elements.dateOfBirth.type = 'date';
        this.elements.dateOfBirth.placeholder = '';

        // Set date limits
        const today = new Date().toISOString().split('T')[0];
        this.elements.dateOfBirth.max = today;
        this.elements.dateOfBirth.min = '1900-01-01';

        // Add change handler for native date picker
        this.elements.dateOfBirth.addEventListener('change', (e) => {
            const dateValue = e.target.value;
            if (dateValue) {
                const date = new Date(dateValue);
                if (this.validateDateOfBirth(date)) {
                    this.selectedDate = date;

                    // Convert to DD-MM-YYYY format for display
                    const day = ('0' + date.getDate()).slice(-2);
                    const month = ('0' + (date.getMonth() + 1)).slice(-2);
                    const year = date.getFullYear();
                    const formattedDate = `${day}-${month}-${year}`;

                    // Update the hint to show selected date
                    const hint = document.querySelector('.date-format-hint');
                    if (hint) {
                        hint.textContent = `Selected: ${formattedDate}`;
                        hint.style.color = 'var(--medical-primary)';
                        hint.style.fontWeight = '600';
                    }

                    this.clearMessages();
                    this.validateForm();
                    this.announceToScreenReader(`Date of birth selected: ${formattedDate}`);
                }
            }
        });

        console.log('✅ Fallback date picker setup complete');
    }

    /**
     * Format file size for display
     */
    formatFileSize(bytes) {
        if (bytes === 0) return '0 Bytes';

        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));

        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }

    /**
     * Create FileList from single file (for compatibility)
     */
    createFileList(file) {
        const dt = new DataTransfer();
        dt.items.add(file);
        return dt.files;
    }

    /**
     * Handle form submission
     */
    async handleSubmit(event) {
        event.preventDefault();

        if (this.state.isUploading) return;

        if (!this.validateForm()) {
            this.showMessage('Please correct the errors and try again.', 'error');
            return;
        }

        try {
            await this.uploadFile();
        } catch (error) {
            console.error('Upload error:', error);
            this.showMessage('Upload failed. Please try again.', 'error');
            this.setUploading(false);
        }
    }

    /**
     * Validate form
     */
    validateForm() {
        let isValid = true;

        // Check if file is selected
        if (!this.state.selectedFile) {
            isValid = false;
        }

        // Validate other relationship field
        if (this.elements.relationshipSelect.value === 'others') {
            const value = this.elements.otherRelationshipInput.value.trim();
            if (!value) {
                isValid = false;
            }
        }

        // Validate other occupation field
        if (this.elements.occupationSelect.value === 'others') {
            const value = this.elements.otherOccupationInput.value.trim();
            if (!value) {
                isValid = false;
            }
        }

        this.updateSubmitButton();
        return isValid;
    }

    /**
     * Update submit button state
     */
    updateSubmitButton() {
        const isValid = this.state.selectedFile && this.validateFormFields();
        this.elements.submitBtn.disabled = !isValid || this.state.isUploading;
    }

    /**
     * Validate form fields only
     */
    validateFormFields() {
        // Check other relationship field
        if (this.elements.relationshipSelect.value === 'others') {
            const value = this.elements.otherRelationshipInput.value.trim();
            if (!value) return false;
        }

        // Check other occupation field
        if (this.elements.occupationSelect.value === 'others') {
            const value = this.elements.otherOccupationInput.value.trim();
            if (!value) return false;
        }

        return true;
    }

    /**
     * Upload file to server
     */
    async uploadFile() {
        this.setUploading(true);
        this.showMessage('Preparing upload...', 'info');

        // Check authentication
        const token = this.getValidToken();
        if (!token) {
            this.redirectToLogin();
            return;
        }

        // Prepare form data
        const formData = this.prepareFormData();

        try {
            const response = await this.performUpload(formData, token);
            this.handleUploadSuccess(response);
        } catch (error) {
            await this.handleUploadError(error);
        }
    }

    /**
     * Prepare form data for upload
     */
    prepareFormData() {
        const formData = new FormData();
        formData.append('audio_file', this.state.selectedFile);
        formData.append('filename', this.state.selectedFile.name);

        // Add relationship
        let relationship = this.elements.relationshipSelect.value;
        if (relationship === 'others') {
            relationship = this.elements.otherRelationshipInput.value.trim() || 'others';
        }
        formData.append('relationship', relationship);

        // Add occupation
        let occupation = this.elements.occupationSelect.value;
        if (occupation === 'others') {
            occupation = this.elements.otherOccupationInput.value.trim() || 'others';
        }
        formData.append('occupation', occupation);

        // Add age from dropdown date picker
        let age = null;
        if (this.selectedDate) {
            age = this.calculateAge(this.selectedDate);
        } else if (this.hiddenDateInput && this.hiddenDateInput.value) {
            // Parse from hidden input (DD-MM-YYYY format)
            const dateValue = this.hiddenDateInput.value;
            if (this.isValidDateFormat(dateValue)) {
                const date = this.parseDate(dateValue);
                if (date) {
                    age = this.calculateAge(date);
                }
            }
        }
        formData.append('age', age !== null ? age.toString() : '');

        return formData;
    }

    /**
     * Perform the actual upload
     */
    performUpload(formData, token) {
        return new Promise((resolve, reject) => {
            const xhr = new XMLHttpRequest();

            xhr.upload.addEventListener('progress', (event) => {
                if (event.lengthComputable) {
                    const progress = Math.round((event.loaded / event.total) * 100);
                    this.updateProgress(progress);
                }
            });

            xhr.addEventListener('load', () => {
                if (xhr.status === 201) {
                    resolve(JSON.parse(xhr.responseText));
                } else {
                    reject(new Error(`HTTP ${xhr.status}: ${xhr.responseText}`));
                }
            });

            xhr.addEventListener('error', () => {
                reject(new Error('Network error occurred'));
            });

            xhr.open('POST', `${this.config.apiBaseUrl}/api/audio_upload/`);
            xhr.setRequestHeader('Authorization', `Bearer ${token}`);
            xhr.send(formData);
        });
    }

    /**
     * Handle successful upload
     */
    handleUploadSuccess(response) {
        this.showMessage('Upload successful! Analyzing audio...', 'success');
        this.updateProgress(100, 'Analysis in progress...');

        setTimeout(() => {
            window.location.href = "/audio_upload/history/";
        }, 2000);
    }

    /**
     * Handle upload error
     */
    async handleUploadError(error) {
        console.error('Upload error:', error);

        if (error.message.includes('HTTP 401')) {
            // Try to refresh token
            const refreshed = await this.refreshToken();
            if (refreshed) {
                // Retry upload
                return this.uploadFile();
            } else {
                this.redirectToLogin();
                return;
            }
        }

        // Parse error message
        let errorMessage = 'Upload failed. Please try again.';
        try {
            const errorData = JSON.parse(error.message.split(': ')[1]);
            errorMessage = Object.values(errorData).join(' ');
        } catch (e) {
            // Use default message
        }

        this.showMessage(errorMessage, 'error');
        this.setUploading(false);
    }

    /**
     * Set uploading state
     */
    setUploading(isUploading) {
        this.state.isUploading = isUploading;

        if (isUploading) {
            this.elements.submitBtn.classList.add('loading');
            this.elements.progressContainer.classList.add('show');
        } else {
            this.elements.submitBtn.classList.remove('loading');
            this.elements.progressContainer.classList.remove('show');
            this.state.uploadProgress = 0;
        }

        this.updateSubmitButton();
    }

    /**
     * Update upload progress
     */
    updateProgress(percent, text = null) {
        this.state.uploadProgress = percent;
        this.elements.progressFill.style.width = `${percent}%`;

        if (text) {
            this.elements.progressText.textContent = text;
        } else {
            this.elements.progressText.textContent = `Uploading... ${percent}%`;
        }
    }

    /**
     * Show message to user
     */
    showMessage(message, type = 'info') {
        const icons = {
            success: 'fas fa-check-circle',
            error: 'fas fa-exclamation-circle',
            info: 'fas fa-info-circle',
            warning: 'fas fa-exclamation-triangle'
        };

        this.elements.messageContainer.innerHTML = `
            <div class="message ${type}">
                <i class="${icons[type]}" aria-hidden="true"></i>
                <span>${message}</span>
            </div>
        `;
    }

    /**
     * Clear messages
     */
    clearMessages() {
        this.elements.messageContainer.innerHTML = '';
    }

    /**
     * Get valid authentication token
     */
    getValidToken() {
        const token = localStorage.getItem('access_token');
        if (!token || this.isTokenExpired(token)) {
            return null;
        }
        return token;
    }

    /**
     * Check if token is expired
     */
    isTokenExpired(token) {
        try {
            // Simple JWT decode without external library
            const payload = JSON.parse(atob(token.split('.')[1]));
            return !payload.exp || payload.exp < (Date.now() / 1000);
        } catch (e) {
            return true;
        }
    }

    /**
     * Refresh authentication token
     */
    async refreshToken() {
        const refresh = localStorage.getItem('refresh_token');
        if (!refresh) return false;

        try {
            const response = await fetch(`${this.config.apiBaseUrl}/api/token/refresh/`, {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ refresh })
            });

            if (response.ok) {
                const data = await response.json();
                localStorage.setItem('access_token', data.access);
                return true;
            }
        } catch (error) {
            console.error('Token refresh failed:', error);
        }

        return false;
    }

    /**
     * Redirect to login page
     */
    redirectToLogin() {
        // Store the current page as redirect destination
        sessionStorage.setItem('redirectAfterLogin', window.location.pathname);
        window.location.href = '/login/';
    }

    /**
     * Announce message to screen readers
     */
    announceToScreenReader(message) {
        const announcement = document.createElement('div');
        announcement.setAttribute('aria-live', 'polite');
        announcement.setAttribute('aria-atomic', 'true');
        announcement.className = 'sr-only';
        announcement.textContent = message;

        document.body.appendChild(announcement);
        setTimeout(() => document.body.removeChild(announcement), 1000);
    }

    // ===== 用户协议相关方法 - 性能优化版本 =====

    /**
     * 预加载弹窗资源 - 简化版本
     */
    preloadTermsModal() {
        // 简单的DOM预热
        if (this.elements.termsModalOverlay) {
            console.log('📋 Terms modal elements ready');
        }
    }



    /**
     * Handle upload area click - Check terms agreement via API call
     */
    async handleUploadAreaClick() {
        console.log('🖱️ Upload area clicked - checking terms agreement via API');

        try {
            // Call backend API to check current terms agreement status
            const hasAgreed = await this.checkTermsAgreementStatus();

            if (hasAgreed) {
                console.log('✅ User has agreed to terms, opening file dialog');
                this.elements.fileInput.click();
            } else {
                console.log('📋 User has not agreed to terms, showing modal');
                this.showTermsModal();
            }
        } catch (error) {
            console.error('❌ Error checking terms agreement:', error);
            // On error, show terms modal as fallback
            this.showTermsModal();
        }
    }

    /**
     * Check terms agreement status via API call
     */
    async checkTermsAgreementStatus() {
        console.log('🔍 Checking terms agreement status via API...');

        try {
            const token = this.getValidToken();
            if (!token) {
                console.warn('⚠️ No valid token for terms check');
                return false;
            }

            const response = await fetch(`${this.config.apiBaseUrl}/api/user/terms-agreement/`, {
                method: 'GET',
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json'
                }
            });

            if (response.ok) {
                const data = await response.json();
                console.log('📋 Terms agreement API response:', data);

                const hasAgreed = data.data.has_agreed_to_terms;
                console.log('📋 User has agreed to terms:', hasAgreed);

                // Update local state
                this.state.hasAgreedToTerms = hasAgreed;

                return hasAgreed;
            } else {
                console.warn('⚠️ Failed to check terms agreement:', response.status);
                return false;
            }
        } catch (error) {
            console.error('❌ Error checking terms agreement:', error);
            return false;
        }
    }

    /**
     * 显示用户协议弹窗 - 简化版本
     */
    showTermsModal() {
        console.log('📋 Attempting to show terms modal...');
        console.log('📋 Modal overlay element:', this.elements.termsModalOverlay);

        if (this.elements.termsModalOverlay.classList.contains('show')) {
            console.log('📋 Modal already shown, returning');
            return;
        }

        this.elements.termsModalOverlay.classList.add('show');
        document.body.style.overflow = 'hidden';
        this.startCountdown();

        console.log('📋 Terms modal shown successfully');
    }

    /**
     * 隐藏用户协议弹窗 - 简化版本
     */
    hideTermsModal() {
        if (!this.elements.termsModalOverlay.classList.contains('show')) {
            return;
        }

        this.elements.termsModalOverlay.classList.remove('show');
        document.body.style.overflow = '';
        this.cleanupCountdown();

        console.log('📋 Terms modal hidden');
    }

    /**
     * 简化的倒计时实现 - 使用基础 setInterval
     */
    startCountdown() {
        if (this.state.countdownActive) return;

        this.state.countdownActive = true;
        let countdown = 5;

        this.elements.termsAcceptBtn.disabled = true;
        this.elements.termsDeclineBtn.disabled = false;
        this.elements.countdownTimer.textContent = countdown;

        this.countdownTimer = setInterval(() => {
            countdown--;
            this.elements.countdownTimer.textContent = Math.max(0, countdown);

            if (countdown <= 0) {
                this.elements.termsAcceptBtn.disabled = false;
                this.state.countdownActive = false;
                clearInterval(this.countdownTimer);
                console.log('⏰ Countdown finished, accept button enabled');
            }
        }, 1000);
    }

    /**
     * 清理倒计时资源
     */
    cleanupCountdown() {
        if (this.countdownTimer) {
            clearInterval(this.countdownTimer);
            this.countdownTimer = null;
        }
        this.state.countdownActive = false;
    }

    /**
     * 处理用户同意协议 - 简化版本
     */
    async handleTermsAccept() {
        if (this.elements.termsAcceptBtn.disabled || this.isSubmittingTerms) {
            return;
        }

        this.isSubmittingTerms = true;
        this.elements.termsAcceptBtn.disabled = true;
        this.elements.termsAcceptBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Processing...';

        try {
            const token = this.getValidToken();
            if (!token) {
                throw new Error('No valid authentication token');
            }

            const response = await fetch(`${this.config.apiBaseUrl}/api/user/terms-agreement/`, {
                method: 'POST',
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({ agreed: true })
            });

            if (response.ok) {
                this.state.hasAgreedToTerms = true;

                // Reset button state
                this.elements.termsAcceptBtn.innerHTML = '<i class="fas fa-check"></i> I Understand and Agree';
                this.elements.termsAcceptBtn.disabled = false;

                this.hideTermsModal();
                this.showMessage('✅ Thank you for agreeing to the user guidelines and privacy policy! You can now upload audio files.', 'success');

                setTimeout(() => {
                    // Check if there's a pending file from drag & drop
                    if (this.state.pendingFile) {
                        console.log('📋 Processing pending file from drag & drop');
                        this.selectFile(this.state.pendingFile);
                        this.state.pendingFile = null; // Clear the pending file
                    } else {
                        // Normal click - open file dialog
                        this.elements.fileInput.click();
                    }
                }, 500);

                console.log('✅ User agreed to terms successfully');
            } else {
                const errorData = await response.json().catch(() => ({}));
                throw new Error(errorData.message || `HTTP ${response.status}`);
            }
        } catch (error) {
            this.elements.termsAcceptBtn.innerHTML = '<i class="fas fa-check"></i> I Understand and Agree';
            this.elements.termsAcceptBtn.disabled = false;

            console.error('❌ Error agreeing to terms:', error);
            this.showMessage(`❌ Agreement failed: ${error.message}`, 'error');
        } finally {
            this.isSubmittingTerms = false;
        }
    }

    /**
     * 处理用户拒绝协议 - 简化版本
     */
    handleTermsDecline() {
        // Clear any pending file
        this.state.pendingFile = null;

        // Ensure state reflects declined status
        this.state.hasAgreedToTerms = false;

        this.hideTermsModal();
        this.showMessage('⚠️ You need to agree to the user guidelines and privacy policy to use the audio upload feature', 'warning');
        console.log('❌ User declined terms');
    }

    /**
     * Reset user terms agreement (for testing purposes)
     * Can be called from browser console: window.uploadApp.resetTermsAgreement()
     */
    async resetTermsAgreement() {
        console.log('🔄 Resetting user terms agreement...');

        try {
            const token = this.getValidToken();
            if (!token) {
                console.error('❌ No valid token for resetting terms');
                return;
            }

            const response = await fetch(`${this.config.apiBaseUrl}/api/user/terms-agreement/`, {
                method: 'DELETE',
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json'
                }
            });

            if (response.ok) {
                const data = await response.json();
                console.log('✅ Terms agreement reset successfully:', data);

                // Update local state
                this.state.hasAgreedToTerms = false;

                this.showMessage('✅ Terms agreement has been reset for testing', 'success');
            } else {
                console.error('❌ Failed to reset terms agreement:', response.status);
                this.showMessage('❌ Failed to reset terms agreement', 'error');
            }
        } catch (error) {
            console.error('❌ Error resetting terms agreement:', error);
            this.showMessage('❌ Error resetting terms agreement', 'error');
        }
    }


}

// Initialize application when DOM is ready
document.addEventListener('DOMContentLoaded', () => {
    window.uploadApp = new AudioUploadApp();
    console.log('✅ Upload app initialized:', window.uploadApp);
});
